/* src/styles/app.css */
/* 导入 Tailwind CSS 样式 */
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
	--color-context-card-border: var(--context-card-border);
	--color-red-800: var(--ds-red-800);
	--color-red-900: var(--ds-red-900);
	--color-blue-700: var(--ds-blue-700);
	--color-amber-800: var(--ds-amber-800);
	--color-amber-850: var(--ds-amber-850);
	--color-gray-100: var(--ds-gray-100);
	--color-gray-400: var(--ds-gray-400);
	--color-gray-700: var(--ds-gray-700);
	--color-gray-1000: var(--ds-gray-1000);
	--color-gray-1000-h: var(--ds-gray-1000-h);
	--color-gray-alpha-200: var(--ds-gray-alpha-200);
	--color-gray-alpha-400: var(--ds-gray-alpha-400);
	--color-background-100: var(--ds-background-100);
	--color-contrast-fg: var(--ds-contrast-fg);
	--color-geist-background: var(--geist-background);
	--shadow-focus-ring: var(--ds-focus-ring);
	--shadow-border-small: var(--ds-shadow-border-small);
	--shadow-menu: var(--ds-shadow-menu);
	--animate-fade-spin: fade-spin 1.2s linear infinite;
}

:root {
	--radius: 0.625rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.145 0 0);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.145 0 0);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.145 0 0);
	--primary: oklch(0.205 0 0);
	--primary-foreground: oklch(0.985 0 0);
	--secondary: oklch(0.97 0 0);
	--secondary-foreground: oklch(0.205 0 0);
	--muted: oklch(0.97 0 0);
	--muted-foreground: oklch(0.556 0 0);
	--accent: oklch(0.97 0 0);
	--accent-foreground: oklch(0.205 0 0);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.922 0 0);
	--input: oklch(0.922 0 0);
	--ring: oklch(0.708 0 0);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: oklch(0.145 0 0);
	--sidebar-primary: oklch(0.205 0 0);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.97 0 0);
	--sidebar-accent-foreground: oklch(0.205 0 0);
	--sidebar-border: oklch(0.922 0 0);
	--sidebar-ring: oklch(0.708 0 0);
	--context-card-border: hsla(0, 0%, 92%, 1);
	--ds-contrast-fg: #ffffff;
	--geist-background: #fff;
	--ds-shadow-menu: var(--ds-shadow-border), 0px 1px 1px rgba(0, 0, 0, 0.02), 0px 4px 8px -4px rgba(0, 0, 0, 0.04), 0px 16px 24px -8px rgba(0, 0, 0, 0.06);
	--ds-red-800: oklch(58.19% 0.2482 25.15);
	--ds-red-900: oklch(54.99% 0.232 25.29);
	--ds-blue-700: oklch(57.61% 0.2508 258.23);
	--ds-amber-800: oklch(77.21% 0.1991 64.28);
	--ds-amber-850: hsl(33, 96%, 42%);
	--ds-gray-100: hsla(0, 0%, 95%, 1);
	--ds-gray-400: hsla(0, 0%, 92%, 1);
	--ds-gray-700: hsla(0, 0%, 56%, 1);
	--ds-gray-1000: hsla(0, 0%, 9%, 1);
	--ds-gray-1000-h: hsl(0, 0%, 22%);
	--ds-gray-alpha-200: hsla(0, 0%, 0%, 0.08);
	--ds-gray-alpha-400: hsla(0, 0%, 0%, 0.08);
	--ds-background-100: hsla(0, 0%, 100%, 1);
	--ds-focus-color: var(--ds-blue-700);
	--ds-focus-ring: 0 0 0 2px var(--ds-background-100), 0 0 0 4px var(--ds-focus-color);
	--ds-shadow-border: 0 0 0 1px rgba(0, 0, 0, 0.08);
	--ds-shadow-small: 0px 2px 2px rgba(0, 0, 0, 0.04);
	--ds-shadow-border-small: var(--ds-shadow-border), var(--ds-shadow-small);
}

.dark {
	--background: oklch(0.145 0 0);
	--foreground: oklch(0.985 0 0);
	--card: oklch(0.205 0 0);
	--card-foreground: oklch(0.985 0 0);
	--popover: oklch(0.205 0 0);
	--popover-foreground: oklch(0.985 0 0);
	--primary: oklch(0.922 0 0);
	--primary-foreground: oklch(0.205 0 0);
	--secondary: oklch(0.269 0 0);
	--secondary-foreground: oklch(0.985 0 0);
	--muted: oklch(0.269 0 0);
	--muted-foreground: oklch(0.708 0 0);
	--accent: oklch(0.269 0 0);
	--accent-foreground: oklch(0.985 0 0);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--context-card-border: hsla(0, 0%, 18%, 1);
	--geist-background: #000;
	--ds-shadow-menu: var(--ds-shadow-border), 0px 1px 1px rgba(0, 0, 0, 0.02), 0px 4px 8px -4px rgba(0, 0, 0, 0.04), 0px 16px 24px -8px rgba(0, 0, 0, 0.06);
	--ds-red-800: oklch(58.01% 0.227 25.12);
	--ds-red-900: oklch(69.96% 0.2136 22.03);
	--ds-blue-700: oklch(57.61% 0.2321 258.23);
	--ds-amber-800: oklch(77.21% 0.1991 64.28);
	--ds-gray-100: hsla(0, 0%, 10%, 1);
	--ds-gray-400: hsla(0, 0%, 18%, 1);
	--ds-gray-700: hsla(0, 0%, 56%, 1);
	--ds-gray-1000: hsla(0, 0%, 93%, 1);
	--ds-gray-1000-h: hsl(0, 0%, 80%);
	--ds-gray-alpha-200: hsla(0, 0%, 100%, 0.09);
	--ds-gray-alpha-400: hsla(0, 0%, 100%, 0.14);
	--ds-background-100: hsla(0,0%,4%, 1);
	--ds-shadow-border: 0 0 0 1px rgba(255, 255, 255, 0.145);
	--ds-shadow-small: 0px 1px 2px rgba(0, 0, 0, 0.16);
	--ds-shadow-border-small: var(--ds-shadow-border), 0px 1px 2px rgba(0, 0, 0, 0.16);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.556 0 0);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.205 0 0);
	--sidebar-foreground: oklch(0.985 0 0);
	--sidebar-primary: oklch(0.488 0.243 264.376);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.269 0 0);
	--sidebar-accent-foreground: oklch(0.985 0 0);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.556 0 0);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}

/* 自定义动画样式 */
@layer utilities {
	/* 渐现动画 */
	.animate-appear {
		animation: appear 0.6s ease-out forwards;
	}

	/* 缩放渐现动画 */
	.animate-appear-zoom {
		animation: appear-zoom 0.8s ease-out forwards;
	}

	/* 渐变底部效果 */
	.fade-bottom {
		mask-image: linear-gradient(
			to bottom,
			black 0%,
			black 90%,
			transparent 100%
		);
	}

	/* 最大容器宽度 */
	.max-w-container {
		max-width: 1200px;
	}
}

/* 动画关键帧定义 */
@keyframes appear {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes appear-zoom {
	from {
		opacity: 0;
		transform: scale(0.95);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes fade-spin {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0.15;
	}
}

/* 品牌颜色变量 - 用于Glow组件 */
:root {
	--brand: var(--primary);
	--brand-foreground: var(--primary-foreground);
}

.dark {
	--brand: var(--primary);
	--brand-foreground: var(--primary-foreground);
}
