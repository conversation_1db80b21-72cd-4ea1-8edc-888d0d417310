import * as React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronRight, FileText } from "lucide-react";

/**
 * 文档引用接口
 */
export interface DocReference {
	index_id: string;
	doc_name: string;
	text: string;
	page_number?: number[];
	title?: string;
	doc_id?: string;
	images?: any[];
}

/**
 * 文档引用组件属性
 */
interface DocReferenceProps {
	references: DocReference[];
	messageId: string;
}

/**
 * 文档引用显示组件
 * 用于显示知识库查询返回的文档引用信息
 */
export function DocReferenceDisplay({ references, messageId }: DocReferenceProps) {
	const [expandedRefs, setExpandedRefs] = React.useState<Set<number>>(new Set());

	const toggleReference = (index: number) => {
		setExpandedRefs(prev => {
			const newSet = new Set(prev);
			if (newSet.has(index)) {
				newSet.delete(index);
			} else {
				newSet.add(index);
			}
			return newSet;
		});
	};

	if (!references || references.length === 0) {
		return null;
	}

	return (
		<div className="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
			<div className="flex items-center gap-2 mb-2">
				<FileText className="h-4 w-4 text-blue-600" />
				<span className="text-sm font-medium text-blue-800">参考文档</span>
				<Badge variant="secondary" className="text-xs">
					{references.length} 个文档
				</Badge>
			</div>
			
			<div className="space-y-2">
				{references.map((ref, index) => (
					<div key={index} className="border border-blue-200 rounded-md bg-white">
						{/* 文档标题栏 */}
						<Button
							variant="ghost"
							className="w-full justify-between p-3 h-auto text-left"
							onClick={() => toggleReference(index)}
						>
							<div className="flex-1">
								<div className="font-medium text-sm text-gray-900">
									{ref.doc_name}
								</div>
								{ref.page_number && ref.page_number.length > 0 && (
									<div className="text-xs text-gray-500 mt-1">
										页码: {ref.page_number.join(', ')}
									</div>
								)}
							</div>
							{expandedRefs.has(index) ? (
								<ChevronDown className="h-4 w-4 text-gray-500" />
							) : (
								<ChevronRight className="h-4 w-4 text-gray-500" />
							)}
						</Button>
						
						{/* 展开的文档内容 */}
						{expandedRefs.has(index) && (
							<div className="px-3 pb-3 border-t border-gray-100">
								{ref.title && (
									<div className="text-sm font-medium text-blue-700 mb-2">
										{ref.title}
									</div>
								)}
								<div className="text-sm text-gray-700 leading-relaxed">
									{ref.text}
								</div>
								{ref.doc_id && (
									<div className="text-xs text-gray-400 mt-2">
										文档ID: {ref.doc_id}
									</div>
								)}
							</div>
						)}
					</div>
				))}
			</div>
		</div>
	);
}
