import { createFileRoute } from "@tanstack/react-router";
import LoaderOne from "@/components/ui/loader-one";
import { LoaderDemo } from "@/components/ui/loader-demo";
import { Button } from "@/components/ui/button";
import * as React from "react";

export const Route = createFileRoute("/loader-test")({
	component: LoaderTestPage,
});

/**
 * LoaderOne 组件测试页面
 * 展示不同场景下的 loader 使用
 */
function LoaderTestPage() {
	const [isLoading, setIsLoading] = React.useState(false);
	const [simulateApiCall, setSimulateApiCall] = React.useState(false);

	// 模拟 API 调用
	const handleSimulateApi = () => {
		setSimulateApiCall(true);
		setTimeout(() => {
			setSimulateApiCall(false);
		}, 3000); // 3秒后停止
	};

	return (
		<div className="min-h-screen bg-gray-50 p-8">
			<div className="max-w-4xl mx-auto space-y-8">
				<div className="text-center">
					<h1 className="text-3xl font-bold text-gray-900 mb-4">
						LoaderOne 组件测试
					</h1>
					<p className="text-gray-600">
						测试对话期间等待响应的 loader 动画效果
					</p>
				</div>

				{/* 基础展示 */}
				<div className="bg-white rounded-lg p-6 shadow-sm border">
					<h2 className="text-xl font-semibold mb-4">基础 LoaderOne 组件</h2>
					<div className="flex items-center justify-center py-8 bg-gray-50 rounded-lg">
						<LoaderOne />
					</div>
				</div>

				{/* LoaderDemo 组件 */}
				<div className="bg-white rounded-lg p-6 shadow-sm border">
					<h2 className="text-xl font-semibold mb-4">LoaderDemo 组件</h2>
					<div className="flex items-center justify-center py-8 bg-gray-50 rounded-lg">
						<LoaderDemo />
					</div>
				</div>

				{/* 交互式测试 */}
				<div className="bg-white rounded-lg p-6 shadow-sm border">
					<h2 className="text-xl font-semibold mb-4">交互式测试</h2>
					<div className="space-y-4">
						<div className="flex gap-4">
							<Button
								onClick={() => setIsLoading(!isLoading)}
								variant={isLoading ? "destructive" : "default"}
							>
								{isLoading ? "停止 Loader" : "启动 Loader"}
							</Button>
							<Button
								onClick={handleSimulateApi}
								disabled={simulateApiCall}
								variant="outline"
							>
								{simulateApiCall ? "API 调用中..." : "模拟 API 调用 (3秒)"}
							</Button>
						</div>

						{/* 显示 loader 状态 */}
						<div className="p-4 bg-gray-50 rounded-lg">
							<p className="text-sm text-gray-600 mb-2">当前状态:</p>
							{(isLoading || simulateApiCall) ? (
								<div className="flex items-center gap-2">
									<LoaderOne />
									<span className="text-sm">
										{simulateApiCall ? "正在调用 API..." : "手动启动的 Loader"}
									</span>
								</div>
							) : (
								<span className="text-sm text-gray-500">无加载状态</span>
							)}
						</div>
					</div>
				</div>

				{/* 聊天气泡中的使用示例 */}
				<div className="bg-white rounded-lg p-6 shadow-sm border">
					<h2 className="text-xl font-semibold mb-4">聊天气泡中的使用示例</h2>
					<div className="space-y-4">
						{/* 用户消息 */}
						<div className="flex justify-end">
							<div className="bg-blue-500 text-white rounded-lg px-4 py-2 max-w-xs">
								你好，请帮我分析一下这个文档
							</div>
						</div>

						{/* AI 响应中的 loader */}
						<div className="flex justify-start">
							<div className="bg-gray-100 rounded-lg px-4 py-2 max-w-xs">
								<div className="flex items-center justify-center py-2">
									<LoaderOne />
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* 按钮中的使用示例 */}
				<div className="bg-white rounded-lg p-6 shadow-sm border">
					<h2 className="text-xl font-semibold mb-4">按钮中的使用示例</h2>
					<div className="space-y-4">
						<Button disabled className="w-full">
							<div className="flex items-center justify-center gap-2">
								<LoaderOne />
								<span>分析中...</span>
							</div>
						</Button>

						<Button disabled variant="outline" className="w-full">
							<div className="flex items-center justify-center gap-2">
								<LoaderOne />
								<span>上传中...</span>
							</div>
						</Button>
					</div>
				</div>

				{/* 使用说明 */}
				<div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
					<h2 className="text-xl font-semibold mb-4 text-blue-900">使用说明</h2>
					<div className="space-y-2 text-blue-800">
						<p>• <strong>基础使用:</strong> 直接导入 LoaderOne 组件并使用</p>
						<p>• <strong>聊天场景:</strong> 在等待 API 响应时显示在聊天气泡中</p>
						<p>• <strong>按钮场景:</strong> 在按钮中显示加载状态</p>
						<p>• <strong>动画效果:</strong> 三个蓝色圆点的跳动动画，循环播放</p>
						<p>• <strong>响应式:</strong> 自适应容器大小</p>
					</div>
				</div>
			</div>
		</div>
	);
}
