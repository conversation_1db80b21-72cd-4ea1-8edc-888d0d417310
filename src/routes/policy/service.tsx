import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export const Route = createFileRoute('/policy/service')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <Button
        variant="ghost"
        className="mb-4"
        asChild
      >
        <Link to="/">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Link>
      </Button>
      <Card>
        <CardHeader>
          <CardTitle className="text-3xl font-bold">服务条款</CardTitle>
          <CardDescription className="text-lg">
            最后更新日期：2024年12月
          </CardDescription>
        </CardHeader>
        <CardContent className="prose prose-gray max-w-none">
          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">1. 接受条款</h2>
            <p>通过访问和使用中化智能助手服务（以下简称"服务"），您同意受这些服务条款（"条款"）的约束。如果您不同意这些条款的任何部分，请不要使用我们的服务。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">2. 服务描述</h2>
            <p>中化智能助手提供基于人工智能的对话服务，旨在帮助用户获取信息、解答问题和提供建议。我们保留随时修改、暂停或终止服务的权利，恕不另行通知。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">3. 用户账户</h2>
            <p>您可能需要创建账户才能使用某些功能。您同意：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>提供准确、最新和完整的注册信息</li>
              <li>维护并及时更新您的账户信息</li>
              <li>保护您的账户凭据的安全性和机密性</li>
              <li>对您的账户下发生的所有活动负责</li>
              <li>立即通知我们任何未经授权的使用或安全漏洞</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">4. 用户行为</h2>
            <p>您同意不将服务用于以下目的：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>违反任何适用的法律或法规</li>
              <li>侵犯他人的权利，包括知识产权</li>
              <li>传播恶意软件或其他有害代码</li>
              <li>收集或存储其他用户的个人信息</li>
              <li>干扰或破坏服务的完整性或性能</li>
              <li>使用自动化手段访问服务，包括机器人、爬虫或类似工具</li>
              <li>试图未经授权访问服务的任何部分</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">5. 知识产权</h2>
            <p>服务及其原创内容、特性和功能归中化集团所有，并受国际版权、商标、专利、商业秘密和其他知识产权法律的保护。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">6. 用户内容</h2>
            <p>您保留对您提交给服务的内容的所有权。但是，通过提交内容，您授予我们在全球范围内、免版税、非独占的许可，以使用、复制、修改、改编、发布、翻译和分发此类内容，仅限于运营和改进服务的目的。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">7. 免责声明</h2>
            <p>服务按"现状"和"可用"基础提供，不提供任何明示或暗示的保证。我们不保证：</p>
            <ul className="list-disc pl-6 space-y-2">
              <li>服务将不间断、安全或无错误</li>
              <li>通过服务获得的信息准确或可靠</li>
              <li>服务的质量将满足您的期望</li>
              <li>服务中的任何错误都将得到纠正</li>
            </ul>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">8. 责任限制</h2>
            <p>在任何情况下，中化集团或其关联公司、董事、员工、代理或供应商均不对任何间接、附带、特殊、后果性或惩罚性损害负责，包括但不限于利润损失、数据丢失、使用损失或业务中断，即使已被告知此类损害的可能性。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">9. 赔偿</h2>
            <p>您同意赔偿并使中化集团及其关联公司、董事、员工、代理和供应商免受因您使用服务、违反这些条款或侵犯任何第三方权利而引起的任何索赔、损害、义务、损失、责任、成本或债务。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">10. 终止</h2>
            <p>我们保留在任何时候以任何理由立即终止或暂停您的账户和对服务的访问的权利，无需事先通知或承担责任，包括但不限于您违反这些条款的情况。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">11. 条款变更</h2>
            <p>我们保留随时修改或替换这些条款的权利。如果变更重大，我们将尽量在任何新条款生效前至少30天提供通知。在修订生效后继续访问或使用我们的服务，即表示您同意受修订条款的约束。</p>
          </section>

          <section className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">12. 适用法律</h2>
            <p>这些条款应受中华人民共和国法律管辖，不考虑其法律冲突规定。您同意接受位于中国北京的法院的专属管辖权。</p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">13. 联系我们</h2>
            <p>如果您对这些条款有任何疑问，请通过以下方式联系我们：</p>
            <p className="mt-2">电子邮件：<a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a></p>
            <p>电话：<a href="tel:+861012345678" className="text-blue-600 hover:underline">+86 10 1234 5678</a></p>
          </section>
        </CardContent>
      </Card>
    </div>
  )
}
