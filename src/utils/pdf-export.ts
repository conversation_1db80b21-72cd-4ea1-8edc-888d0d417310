import jsPD<PERSON> from 'jspdf';

/**
 * PDF Export utility functions
 * Supports exporting analysis results to PDF format
 */

interface AnalysisResult {
  origin: string;
  issueDes: string;
  suggestion: string;
  reason: string;
}

/**
 * Export analysis results to PDF
 * @param results - Array of analysis results
 * @param fileName - Optional custom file name
 */
export async function exportAnalysisResultsToPDF(
  results: AnalysisResult[],
  fileName?: string
): Promise<void> {
  try {
    // Create new PDF document
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Set font to support Chinese characters
    // Note: jsPDF has limited Chinese font support by default
    // For better Chinese support, you might need to add custom fonts
    
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 20;
    const contentWidth = pageWidth - 2 * margin;
    let currentY = margin;

    // Add title
    pdf.setFontSize(20);
    pdf.text('Analysis Report / 分析报告', margin, currentY);
    currentY += 15;

    // Add generation date
    pdf.setFontSize(12);
    const currentDate = new Date().toLocaleString('zh-CN');
    pdf.text(`Generated on / 生成时间: ${currentDate}`, margin, currentY);
    currentY += 15;

    // Add summary
    pdf.setFontSize(14);
    pdf.text(`Total Issues Found / 发现问题总数: ${results.length}`, margin, currentY);
    currentY += 20;

    // Process each analysis result
    results.forEach((result, index) => {
      // Check if we need a new page
      if (currentY > pageHeight - 60) {
        pdf.addPage();
        currentY = margin;
      }

      // Issue number
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`Issue ${index + 1} / 问题 ${index + 1}`, margin, currentY);
      currentY += 10;

      // Original text section
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Original Text / 原文内容:', margin, currentY);
      currentY += 8;

      pdf.setFont('helvetica', 'normal');
      const originLines = pdf.splitTextToSize(result.origin, contentWidth);
      pdf.text(originLines, margin, currentY);
      currentY += originLines.length * 6 + 5;

      // Issue description section
      if (currentY > pageHeight - 40) {
        pdf.addPage();
        currentY = margin;
      }
      
      pdf.setFont(undefined, 'bold');
      pdf.text('Issue Description / 问题描述:', margin, currentY);
      currentY += 8;
      
      pdf.setFont(undefined, 'normal');
      const issueLines = pdf.splitTextToSize(result.issueDes, contentWidth);
      pdf.text(issueLines, margin, currentY);
      currentY += issueLines.length * 6 + 5;

      // Suggestion section
      if (currentY > pageHeight - 40) {
        pdf.addPage();
        currentY = margin;
      }
      
      pdf.setFont(undefined, 'bold');
      pdf.text('Improvement Suggestion / 改进建议:', margin, currentY);
      currentY += 8;
      
      pdf.setFont(undefined, 'normal');
      const suggestionLines = pdf.splitTextToSize(result.suggestion, contentWidth);
      pdf.text(suggestionLines, margin, currentY);
      currentY += suggestionLines.length * 6 + 5;

      // Reason section
      if (currentY > pageHeight - 40) {
        pdf.addPage();
        currentY = margin;
      }
      
      pdf.setFont(undefined, 'bold');
      pdf.text('Basis / 依据说明:', margin, currentY);
      currentY += 8;
      
      pdf.setFont(undefined, 'normal');
      const reasonLines = pdf.splitTextToSize(result.reason, contentWidth);
      pdf.text(reasonLines, margin, currentY);
      currentY += reasonLines.length * 6 + 15;

      // Add separator line
      if (index < results.length - 1) {
        pdf.setDrawColor(200, 200, 200);
        pdf.line(margin, currentY, pageWidth - margin, currentY);
        currentY += 10;
      }
    });

    // Generate filename
    const defaultFileName = `analysis-report-${new Date().toISOString().split('T')[0]}.pdf`;
    const finalFileName = fileName || defaultFileName;

    // Save the PDF
    pdf.save(finalFileName);

    console.log(`PDF exported successfully: ${finalFileName}`);
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error(`PDF导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * Export simple text content to PDF
 * @param content - Text content to export
 * @param title - Document title
 * @param fileName - Optional custom file name
 */
export async function exportTextToPDF(
  content: string,
  title: string = 'Document',
  fileName?: string
): Promise<void> {
  try {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 20;
    const contentWidth = pageWidth - 2 * margin;
    let currentY = margin;

    // Add title
    pdf.setFontSize(18);
    pdf.setFont(undefined, 'bold');
    pdf.text(title, margin, currentY);
    currentY += 15;

    // Add generation date
    pdf.setFontSize(10);
    pdf.setFont(undefined, 'normal');
    const currentDate = new Date().toLocaleString('zh-CN');
    pdf.text(`Generated on: ${currentDate}`, margin, currentY);
    currentY += 15;

    // Add content
    pdf.setFontSize(12);
    const lines = pdf.splitTextToSize(content, contentWidth);
    
    lines.forEach((line: string) => {
      if (currentY > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
      }
      pdf.text(line, margin, currentY);
      currentY += 6;
    });

    // Generate filename
    const defaultFileName = `document-${new Date().toISOString().split('T')[0]}.pdf`;
    const finalFileName = fileName || defaultFileName;

    // Save the PDF
    pdf.save(finalFileName);

    console.log(`PDF exported successfully: ${finalFileName}`);
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error(`PDF导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}
