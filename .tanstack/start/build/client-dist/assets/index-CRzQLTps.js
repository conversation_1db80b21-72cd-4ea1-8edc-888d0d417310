const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-DbVZ_fy7.js","assets/main-B6mr56sS.js"])))=>i.map(i=>d[i]);
import{r as i,j as s,g as za,R as de,a as Ar,b as Ua,c as Ha,u as Mr,_ as Yn,d as Wa}from"./main-B6mr56sS.js";import{t as Va,a as vn,c as M,b as Ga}from"./index-_TRYHs0w.js";import{M as Ka,i as Ya,u as Dr,P as qa,a as Xa,b as Za,c as Ja,m as xn,L as qn}from"./loader-one-DvFwIHNv.js";import{c as Z}from"./createLucideIcon-3aflogHk.js";import{L as nn,u as Se,c as ve,P as Y,d as Pr,e as je,f as Qa,S as ei,A as _r,a as Tr,b as Ir}from"./loader-circle-C4214kKS.js";import{c as ti,p as ni,u as ue,a as Or}from"./use-auth-BT4cMsMh.js";import{d as ee,e as K,f as oe,m as Ne,s as Ut,h as ri,i as bn,u as oi,j as si}from"./user-B82y3N16.js";import{u as q,c as gt,a as kr,B as Q,S as Ze,b as Lr,d as Fr}from"./button-B5f_3GyS.js";import{U as ai}from"./user-Dsb5yfVu.js";import{B as ii}from"./badge-czNfS6QW.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],$r=Z("check",ci);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const li=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Br=Z("chevron-down",li);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const di=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],zr=Z("chevron-right",di);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ui=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],fi=Z("chevron-up",ui);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pi=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],mi=Z("circle",pi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],gi=Z("copy",hi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vi=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],xi=Z("file-text",vi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bi=[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]],wi=Z("image-plus",bi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yi=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],Ci=Z("log-out",yi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ni=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Si=Z("menu",Ni);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ei=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],Ri=Z("message-square",Ei);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ji=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]],Ai=Z("panel-left-close",ji);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mi=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m14 9 3 3-3 3",key:"8010ee"}]],Di=Z("panel-left-open",Mi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pi=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],_i=Z("panel-left",Pi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ti=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],Ii=Z("pen-line",Ti);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oi=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],ki=Z("plus",Oi);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Li=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Fi=Z("trash-2",Li);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $i=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Et=Z("x",$i);class Bi extends i.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=n.offsetParent,o=Ya(r)&&r.offsetWidth||0,a=this.props.sizeRef.current;a.height=n.offsetHeight||0,a.width=n.offsetWidth||0,a.top=n.offsetTop,a.left=n.offsetLeft,a.right=o-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function zi({children:e,isPresent:t,anchorX:n,root:r}){const o=i.useId(),a=i.useRef(null),c=i.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=i.useContext(Ka);return i.useInsertionEffect(()=>{const{width:d,height:u,top:f,left:p,right:g}=c.current;if(t||!a.current||!d||!u)return;const v=n==="left"?`left: ${p}`:`right: ${g}`;a.current.dataset.motionPopId=o;const h=document.createElement("style");l&&(h.nonce=l);const m=r??document.head;return m.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${d}px !important;
            height: ${u}px !important;
            ${v}px !important;
            top: ${f}px !important;
          }
        `),()=>{m.contains(h)&&m.removeChild(h)}},[t]),s.jsx(Bi,{isPresent:t,childRef:a,sizeRef:c,children:i.cloneElement(e,{ref:a})})}const Ui=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:a,mode:c,anchorX:l,root:d})=>{const u=Dr(Hi),f=i.useId();let p=!0,g=i.useMemo(()=>(p=!1,{id:f,initial:t,isPresent:n,custom:o,onExitComplete:v=>{u.set(v,!0);for(const h of u.values())if(!h)return;r&&r()},register:v=>(u.set(v,!1),()=>u.delete(v))}),[n,u,r]);return a&&p&&(g={...g}),i.useMemo(()=>{u.forEach((v,h)=>u.set(h,!1))},[n]),i.useEffect(()=>{!n&&!u.size&&r&&r()},[n]),c==="popLayout"&&(e=s.jsx(zi,{isPresent:n,anchorX:l,root:d,children:e})),s.jsx(qa.Provider,{value:g,children:e})};function Hi(){return new Map}const st=e=>e.key||"";function Xn(e){const t=[];return i.Children.forEach(e,n=>{i.isValidElement(n)&&t.push(n)}),t}const Ur=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:a="sync",propagate:c=!1,anchorX:l="left",root:d})=>{const[u,f]=Xa(c),p=i.useMemo(()=>Xn(e),[e]),g=c&&!u?[]:p.map(st),v=i.useRef(!0),h=i.useRef(p),m=Dr(()=>new Map),[x,b]=i.useState(p),[w,y]=i.useState(p);Za(()=>{v.current=!1,h.current=p;for(let R=0;R<w.length;R++){const N=st(w[R]);g.includes(N)?m.delete(N):m.get(N)!==!0&&m.set(N,!1)}},[w,g.length,g.join("-")]);const C=[];if(p!==x){let R=[...p];for(let N=0;N<w.length;N++){const D=w[N],E=st(D);g.includes(E)||(R.splice(N,0,D),C.push(D))}return a==="wait"&&C.length&&(R=C),y(Xn(R)),b(p),null}const{forceRender:S}=i.useContext(Ja);return s.jsx(s.Fragment,{children:w.map(R=>{const N=st(R),D=c&&!u?!1:p===w||g.includes(N),E=()=>{if(m.has(N))m.set(N,!0);else return;let T=!0;m.forEach(O=>{O||(T=!1)}),T&&(S?.(),y(h.current),c&&f?.(),r&&r())};return s.jsx(Ui,{isPresent:D,initial:!v.current||n?void 0:!1,custom:t,presenceAffectsLayout:o,mode:a,root:d,onExitComplete:D?void 0:E,anchorX:l,children:R},N)})})};var Ht,Zn;function Wi(){return Zn||(Zn=1,Ht=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,o,a;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(o=r;o--!==0;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(a=Object.keys(t),r=a.length,r!==Object.keys(n).length)return!1;for(o=r;o--!==0;)if(!Object.prototype.hasOwnProperty.call(n,a[o]))return!1;for(o=r;o--!==0;){var c=a[o];if(!e(t[c],n[c]))return!1}return!0}return t!==t&&n!==n}),Ht}var Vi=Wi();const Gi=za(Vi),Ki=(...e)=>e.filter(Boolean).join(" "),vt=(...e)=>Va(Ki(e)),Yi=vn("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-black text-white hover:bg-gray-800",destructive:"border border-black text-black hover:bg-gray-100",outline:"border border-gray-400 bg-white hover:bg-gray-100 hover:text-black",secondary:"bg-gray-200 text-black hover:bg-gray-300",ghost:"text-black hover:bg-gray-100 hover:text-black",link:"text-black underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default"}}),Je=de.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},a)=>s.jsx("button",{className:vt(Yi({variant:t,size:n,className:e})),ref:a,...o}));Je.displayName="Button";const Hr=de.forwardRef(({className:e,...t},n)=>s.jsx("textarea",{className:vt("flex min-h-[80px] w-full rounded-md border border-gray-400 bg-white px-3 py-2 text-base ring-offset-white placeholder:text-black focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-black",e),ref:n,...t}));Hr.displayName="Textarea";const qi=({size:e=16})=>s.jsx("svg",{height:e,viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3H13V13H3V3Z",fill:"currentColor"})}),Xi=({size:e=16})=>s.jsx("svg",{height:e,strokeLinejoin:"round",viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z",fill:"currentColor"})});function Zi({onSelectAction:e}){const t=[{title:"受限空间内气体环境满足作业要求下",label:"气体检测分析合格标准是多少",action:"受限空间内气体环境满足作业要求下，气体检测分析合格标准是多少"},{title:"受限空间作业前,应根据受限空间",label:"盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准",action:"受限空间作业前,应根据受限空间盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准"},{title:"固定式配电箱及开关箱的底面",label:"离地面垂直高度的高度是多少",action:"固定式配电箱及开关箱的底面离地面垂直高度的高度是多少"},{title:"八大保命原则",label:"是什么",action:"中化集团八大保命原则"}];return s.jsx("div",{"data-testid":"suggested-actions",className:"grid pb-2 grid-cols-1 sm:grid-cols-2 gap-2 w-full",children:s.jsx(Ur,{children:t.map((n,r)=>s.jsx(xn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*r},className:"block",children:s.jsxs(Je,{variant:"ghost",onClick:()=>e(n.action),className:`text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start
                       border-gray-300 bg-white hover:bg-gray-100 text-black hover:text-gray-900`,children:[s.jsx("span",{className:"font-medium",children:n.title}),s.jsx("span",{className:"text-gray-500",children:n.label})]})},`suggested-action-${r}`))})})}const Ji=i.memo(Zi,(e,t)=>!(e.chatId!==t.chatId||e.selectedVisibilityType!==t.selectedVisibilityType)),Jn=({attachment:e,isUploading:t=!1})=>{const{name:n,url:r,contentType:o}=e;return s.jsxs("div",{"data-testid":"input-attachment-preview",className:"flex flex-col gap-1",children:[s.jsxs("div",{className:"w-20 h-16 aspect-video bg-gray-200 rounded-md relative flex flex-col items-center justify-center overflow-hidden border border-gray-300",children:[o?.startsWith("image/")&&r?s.jsx("img",{src:r,alt:n??"An image attachment",className:"rounded-md size-full object-cover grayscale"},r):s.jsxs("div",{className:"flex items-center justify-center text-xs text-gray-600 text-center p-1",children:["File: ",n?.split(".").pop()?.toUpperCase()||"Unknown"]}),t&&s.jsx("div",{"data-testid":"input-attachment-loader",className:"animate-spin absolute text-gray-500",children:s.jsx(nn,{className:"size-5"})})]}),s.jsx("div",{className:"text-xs text-gray-600 max-w-20 truncate",children:n})]})};function Qi({onStop:e}){return s.jsx(Je,{"data-testid":"stop-button",className:"rounded-full p-1.5 h-fit border border-black text-white",onClick:t=>{t.preventDefault(),e()},"aria-label":"Stop generating",children:s.jsx(qi,{size:14})})}const ec=i.memo(Qi,(e,t)=>e.onStop===t.onStop);function tc({submitForm:e,input:t,uploadQueue:n,attachments:r,canSend:o,isGenerating:a}){const c=n.length>0||!o||a||t.trim().length===0&&r.length===0;return s.jsx(Je,{"data-testid":"send-button",className:"rounded-full p-1.5 h-fit",onClick:l=>{l.preventDefault(),c||e()},disabled:c,"aria-label":"Send message",children:s.jsx(Xi,{size:14})})}const nc=i.memo(tc,(e,t)=>!(e.input!==t.input||e.uploadQueue.length!==t.uploadQueue.length||e.attachments.length!==t.attachments.length||e.attachments.length>0&&!Gi(e.attachments,t.attachments)||e.canSend!==t.canSend||e.isGenerating!==t.isGenerating));function rc({chatId:e,messages:t,attachments:n,setAttachments:r,onSendMessage:o,onStopGenerating:a,isGenerating:c,canSend:l,className:d,selectedVisibilityType:u}){const f=i.useRef(null),p=i.useRef(null),[g,v]=i.useState(""),[h,m]=i.useState([]),x=()=>{const E=f.current;E&&(E.style.height="auto",E.style.height=`${E.scrollHeight+2}px`)},b=i.useCallback(()=>{const E=f.current;E&&(E.style.height="auto",E.rows=1,x())},[]);i.useEffect(()=>{f.current&&x()},[g]);const w=E=>{v(E.target.value)},y=async E=>(console.log(`MOCK: Simulating upload for file: ${E.name}`),new Promise(T=>{setTimeout(()=>{try{const W={url:URL.createObjectURL(E),name:E.name,contentType:E.type||"application/octet-stream",size:E.size};console.log(`MOCK: Upload successful for ${E.name}`),T(W)}catch(O){console.error("MOCK: Failed to create object URL for preview:",O),T(void 0)}finally{m(O=>O.filter(W=>W!==E.name))}},700)})),C=i.useCallback(async E=>{const T=Array.from(E.target.files||[]);if(T.length===0)return;m($=>[...$,...T.map(k=>k.name)]),p.current&&(p.current.value="");const O=25*1024*1024,W=T.filter($=>$.size<=O),H=T.filter($=>$.size>O);H.length>0&&(console.warn(`Skipped ${H.length} files larger than ${O/1024/1024}MB.`),m($=>$.filter(k=>!H.some(A=>A.name===k))));const U=W.map($=>y($)),z=(await Promise.all(U)).filter($=>$!==void 0);r($=>[...$,...z])},[r,y]),S=i.useCallback(E=>{E.url.startsWith("blob:")&&URL.revokeObjectURL(E.url),r(T=>T.filter(O=>O.url!==E.url||O.name!==E.name)),f.current?.focus()},[r,f]),R=i.useCallback(()=>{if(g.trim().length===0&&n.length===0){console.warn("Please enter a message or add an attachment.");return}o({input:g,attachments:n}),v(""),r([]),n.forEach(E=>{E.url.startsWith("blob:")&&URL.revokeObjectURL(E.url)}),b(),f.current?.focus()},[g,n,o,r,f,b]),N=t.length===0&&n.length===0&&h.length===0,D=c||h.length>0;return s.jsxs("div",{className:vt("relative w-full flex flex-col gap-4",d),children:[s.jsx(Ur,{children:N&&s.jsx(xn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.2},children:s.jsx(Ji,{onSelectAction:E=>{v(E),requestAnimationFrame(()=>{x(),f.current?.focus()})},chatId:e,selectedVisibilityType:u})},"suggested-actions-container")}),s.jsx("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:p,multiple:!0,onChange:C,tabIndex:-1,disabled:D,accept:"image/*,video/*,audio/*,.pdf"}),(n.length>0||h.length>0)&&s.jsxs("div",{"data-testid":"attachments-preview",className:"flex pt-[10px] flex-row gap-3 overflow-x-auto items-end pb-2 pl-1",children:[n.map(E=>s.jsxs("div",{className:"relative group",children:[s.jsx(Jn,{attachment:E,isUploading:!1}),s.jsx(Je,{variant:"destructive",size:"icon",className:"absolute top-[-8px] right-[-8px] h-5 w-5 rounded-full p-0 flex items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>S(E),"aria-label":`Remove ${E.name}`,children:s.jsx(Et,{className:"size-3"})})]},E.url||E.name)),h.map((E,T)=>s.jsx(Jn,{attachment:{url:"",name:E,contentType:"",size:0},isUploading:!0},`upload-${E}-${T}`))]}),s.jsx(Hr,{"data-testid":"multimodal-input",ref:f,placeholder:"你想问什么",value:g,onChange:w,className:vt("min-h-[24px] max-h-[calc(75dvh)] overflow-y-auto resize-none rounded-2xl !text-base pb-10","bg-gray-100 border border-gray-300",d),style:{color:"black"},rows:1,autoFocus:!0,disabled:!l||c||h.length>0,onKeyDown:E=>{E.key==="Enter"&&!E.shiftKey&&!E.nativeEvent.isComposing&&(E.preventDefault(),l&&!c&&h.length===0&&(g.trim().length>0||n.length>0)&&R())}}),s.jsx("div",{className:"absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end z-10",children:c?s.jsx(ec,{onStop:a}):s.jsx(nc,{submitForm:R,input:g,uploadQueue:h,attachments:n,canSend:l,isGenerating:c})})]})}function oc({onSend:e,showSuggestedActions:t=!0}={}){const[n,r]=i.useState([]),[o,a]=i.useState(!1),[c]=i.useState("demo-input-only"),[l,d]=i.useState(t),u=i.useCallback(({input:v,attachments:h})=>{console.log("--- 发送消息 ---"),console.log("输入:",v),console.log("附件:",h),console.log("---------------------------------"),e&&v.trim()&&e(v.trim()),a(!0),setTimeout(()=>{a(!1),r([])},500)},[e]),f=i.useCallback(()=>{console.log("停止按钮被点击（模拟）。"),a(!1)},[]);return s.jsx("div",{className:"w-full max-w-3xl mx-auto p-4",children:s.jsxs("div",{className:"flex flex-col gap-4",children:[t&&s.jsx("div",{className:"flex justify-center",children:s.jsx("button",{onClick:()=>d(!l),className:"p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm",title:l?"收起快捷输入":"展开快捷输入",children:l?s.jsx(fi,{className:"w-4 h-4 text-gray-600"}):s.jsx(Br,{className:"w-4 h-4 text-gray-600"})})}),s.jsx("div",{children:s.jsx(rc,{chatId:c,messages:t&&l?[]:[{id:"dummy",content:"",role:"user"}],attachments:n,setAttachments:r,onSendMessage:u,onStopGenerating:f,isGenerating:o,canSend:!0,selectedVisibilityType:"private"})})]})})}const Rt=i.forwardRef(({className:e,type:t,...n},r)=>s.jsx("input",{type:t,className:M("flex h-9 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm text-foreground shadow-sm shadow-black/5 transition-shadow placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:cursor-not-allowed disabled:opacity-50",t==="search"&&"[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none",t==="file"&&"p-0 pr-3 italic text-muted-foreground/70 file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:border-input file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic file:text-foreground",e),ref:r,...n}));Rt.displayName="Input";function sc({onUpload:e}={}){const t=i.useRef(null),n=i.useRef(null),[r,o]=i.useState(null),[a,c]=i.useState(null),l=i.useCallback(()=>{n.current?.click()},[]),d=i.useCallback(f=>{const p=f.target.files?.[0];if(p){c(p.name);const g=URL.createObjectURL(p);o(g),t.current=g,e?.(g)}},[e]),u=i.useCallback(()=>{r&&URL.revokeObjectURL(r),o(null),c(null),t.current=null,n.current&&(n.current.value="")},[r]);return i.useEffect(()=>()=>{t.current&&URL.revokeObjectURL(t.current)},[]),{previewUrl:r,fileName:a,fileInputRef:n,handleThumbnailClick:l,handleFileChange:d,handleRemove:u}}function ac({onFileSelect:e,fileContent:t}){const{fileName:n,fileInputRef:r,handleThumbnailClick:o,handleFileChange:a,handleRemove:c}=sc({onUpload:m=>console.log("Uploaded image URL:",m)}),[l,d]=i.useState(!1),u=i.useCallback(m=>{a(m);const x=m.target.files?.[0]||null;e?.(x)},[a,e]),f=i.useCallback(()=>{c(),e?.(null)},[c,e]),p=m=>{m.preventDefault(),m.stopPropagation()},g=m=>{m.preventDefault(),m.stopPropagation(),d(!0)},v=m=>{m.preventDefault(),m.stopPropagation(),d(!1)},h=i.useCallback(m=>{m.preventDefault(),m.stopPropagation(),d(!1);const x=m.dataTransfer.files?.[0];if(x){if(r.current){const b=new DataTransfer;b.items.add(x),r.current.files=b.files;const w=new Event("change",{bubbles:!0});r.current.dispatchEvent(w)}e?.(x)}},[r,e]);return s.jsxs("div",{className:"w-full max-w-md space-y-6 rounded-xl border border-border bg-card p-6 shadow-sm",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx("h3",{className:"text-lg font-medium",children:"文件上传"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"支持格式: TXT, PDF, DOCX"})]}),s.jsx(Rt,{type:"file",accept:".txt,.pdf,.docx,text/plain,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document",className:"hidden",ref:r,onChange:u}),n?s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"relative h-64 overflow-hidden rounded-lg border bg-background",children:s.jsx("div",{className:"h-full w-full p-4 overflow-auto",children:t?s.jsx("div",{className:"text-sm text-foreground whitespace-pre-wrap font-mono leading-relaxed",children:t}):s.jsx("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm font-medium",children:"Preview"}),s.jsx("p",{className:"text-xs",children:"文件内容将在这里显示"})]})})})}),n&&s.jsxs("div",{className:"mt-2 flex items-center gap-2 text-sm text-muted-foreground",children:[s.jsx("span",{className:"truncate",children:n}),s.jsx("button",{onClick:f,className:"ml-auto rounded-full p-1 hover:bg-muted",children:s.jsx(Et,{className:"h-4 w-4"})})]})]}):s.jsxs("div",{onClick:o,onDragOver:p,onDragEnter:g,onDragLeave:v,onDrop:h,className:M("flex h-64 cursor-pointer flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/50 transition-colors hover:bg-muted",l&&"border-primary/50 bg-primary/5"),children:[s.jsx("div",{className:"rounded-full bg-background p-3 shadow-sm",children:s.jsx(wi,{className:"h-6 w-6 text-muted-foreground"})}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm font-medium",children:"点击选择文件"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"或拖拽文件到此处"})]})]})]})}function ic({text:e,selected:t,setSelected:n,discount:r=!1}){return s.jsxs("button",{onClick:()=>n(e),className:M("relative w-fit px-4 py-2 text-sm font-semibold capitalize","text-foreground transition-colors",r&&"flex items-center justify-center gap-2.5"),children:[s.jsx("span",{className:"relative z-10",children:e}),t&&s.jsx(xn.span,{layoutId:"tab",transition:{type:"spring",duration:.4},className:"absolute inset-0 z-0 rounded-full bg-background shadow-sm"})]})}async function cc(e,t){const n={id:t.id,userId:e,title:t.title,mode:t.mode||"无忧问答"};return(await ee.insert(K).values(n).returning())[0]}async function lc(e){return await ee.select().from(K).where(oe(K.userId,e)).orderBy(ri(K.updatedAt))}async function dc(){try{await ee.update(K).set({mode:"无忧问答"}).where(Ut`mode IS NULL OR mode = '' OR mode = '知识库查询'`),await ee.update(K).set({mode:"无忧分析师"}).where(Ut`mode = '方案改进'`),await ee.update(K).set({mode:"无忧计算师"}).where(Ut`mode = '数据计算'`),console.log("已修复对话的 mode 字段")}catch(e){console.error("修复对话 mode 字段失败:",e)}}async function uc(e,t){return(await ee.select().from(K).where(bn(oe(K.id,e),oe(K.userId,t))).limit(1))[0]||null}async function fc(e,t,n){return(await ee.update(K).set({title:n,updatedAt:new Date().toISOString()}).where(bn(oe(K.id,e),oe(K.userId,t))).returning()).length>0}async function pc(e,t){return await ee.delete(Ne).where(oe(Ne.conversationId,e)),(await ee.delete(K).where(bn(oe(K.id,e),oe(K.userId,t))).returning()).length>0}async function mc(e){const t={id:e.id,conversationId:e.conversationId,content:e.content,role:e.role,docReferences:e.docReferences?JSON.stringify(e.docReferences):null};return await ee.update(K).set({updatedAt:new Date().toISOString()}).where(oe(K.id,e.conversationId)),(await ee.insert(Ne).values(t).returning())[0]}async function hc(e){return(await ee.select().from(Ne).where(oe(Ne.conversationId,e)).orderBy(Ne.timestamp)).map(n=>({...n,docReferences:n.docReferences?JSON.parse(n.docReferences):void 0}))}async function Qn(e,t){const n=await uc(e,t);if(!n)return null;const r=await hc(e);return{conversation:n,messages:r}}async function gc(e){const t=await ee.select({id:K.id}).from(K).where(oe(K.userId,e));for(const n of t)await ee.delete(Ne).where(oe(Ne.conversationId,n.id));await ee.delete(K).where(oe(K.userId,e))}const rn=ti()(ni((e,t)=>({conversations:[],currentConversationId:null,isLoading:!1,error:null,createConversation:async(n="新对话",r="无忧问答")=>{const a=ue.getState().user;if(!a)throw new Error("用户未登录");e({isLoading:!0,error:null});try{const c=`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await cc(a.id,{id:c,title:n,mode:r});const l={id:c,title:n,mode:r,messages:[],createdAt:new Date,updatedAt:new Date};return e(d=>({conversations:[l,...d.conversations],currentConversationId:c,isLoading:!1})),c}catch(c){const l=c instanceof Error?c.message:"创建对话失败";throw e({error:l,isLoading:!1}),c}},deleteConversation:async n=>{const o=ue.getState().user;if(!o)throw new Error("用户未登录");e({isLoading:!0,error:null});try{await pc(n,o.id),e(a=>{const c=a.conversations.filter(d=>d.id!==n),l=a.currentConversationId===n?c.length>0?c[0].id:null:a.currentConversationId;return{conversations:c,currentConversationId:l,isLoading:!1}})}catch(a){const c=a instanceof Error?a.message:"删除对话失败";throw e({error:c,isLoading:!1}),a}},updateConversationTitle:async(n,r)=>{const a=ue.getState().user;if(!a)throw new Error("用户未登录");e({isLoading:!0,error:null});try{await fc(n,a.id,r),e(c=>({conversations:c.conversations.map(l=>l.id===n?{...l,title:r,updatedAt:new Date}:l),isLoading:!1}))}catch(c){const l=c instanceof Error?c.message:"更新标题失败";throw e({error:l,isLoading:!1}),c}},setCurrentConversation:n=>{e({currentConversationId:n})},addMessage:async(n,r,o,a)=>{e({isLoading:!0,error:null});try{const c=`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await mc({id:c,conversationId:n,content:r,role:o,docReferences:a});const l={id:c,content:r,role:o,timestamp:new Date,docReferences:a};e(d=>({conversations:d.conversations.map(u=>u.id===n?{...u,messages:[...u.messages,l],updatedAt:new Date}:u),isLoading:!1}))}catch(c){const l=c instanceof Error?c.message:"添加消息失败";throw e({error:l,isLoading:!1}),c}},getCurrentConversation:()=>{const n=t();return n.conversations.find(r=>r.id===n.currentConversationId)||null},getConversationsByMode:n=>t().conversations.filter(o=>o.mode===n),loadUserConversations:async()=>{const r=ue.getState().user;if(!r){e({conversations:[],currentConversationId:null});return}e({isLoading:!0,error:null});try{await dc();const o=await lc(r.id),a=await Promise.all(o.map(async c=>{const l=await Qn(c.id,r.id);return{id:c.id,title:c.title,mode:c.mode||"无忧问答",messages:l?.messages.map(d=>({id:d.id,content:d.content,role:d.role,timestamp:new Date(d.timestamp),docReferences:d.docReferences}))||[],createdAt:new Date(c.createdAt),updatedAt:new Date(c.updatedAt)}}));e({conversations:a,isLoading:!1,currentConversationId:t().currentConversationId&&a.find(c=>c.id===t().currentConversationId)?t().currentConversationId:null})}catch(o){const a=o instanceof Error?o.message:"加载对话失败";e({error:a,isLoading:!1})}},syncConversationWithDB:async n=>{const o=ue.getState().user;if(o)try{const a=await Qn(n,o.id);if(!a)return;const c={id:a.conversation.id,title:a.conversation.title,mode:a.conversation.mode||"无忧问答",messages:a.messages.map(l=>({id:l.id,content:l.content,role:l.role,timestamp:new Date(l.timestamp),docReferences:l.docReferences})),createdAt:new Date(a.conversation.createdAt),updatedAt:new Date(a.conversation.updatedAt)};e(l=>({conversations:l.conversations.map(d=>d.id===n?c:d)}))}catch(a){console.error("同步对话失败:",a)}},clearAllConversations:async()=>{const r=ue.getState().user;if(!r){e({conversations:[],currentConversationId:null});return}e({isLoading:!0,error:null});try{await gc(r.id),e({conversations:[],currentConversationId:null,isLoading:!1})}catch(o){const a=o instanceof Error?o.message:"清空对话失败";throw e({error:a,isLoading:!1}),o}},setLoading:n=>{e({isLoading:n})},setError:n=>{e({error:n})}}),{name:"conversation-store",partialize:e=>({currentConversationId:e.currentConversationId})})),Wt=768;function vc(){const[e,t]=i.useState(void 0);return i.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Wt-1}px)`),r=()=>{t(window.innerWidth<Wt)};return n.addEventListener("change",r),t(window.innerWidth<Wt),()=>n.removeEventListener("change",r)},[]),!!e}function L(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}var xc=Ar[" useId ".trim().toString()]||(()=>{}),bc=0;function Ae(e){const[t,n]=i.useState(xc());return Se(()=>{n(r=>r??String(bc++))},[e]),e||(t?`radix-${t}`:"")}var wc=Ar[" useInsertionEffect ".trim().toString()]||Se;function jt({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,a,c]=yc({defaultProp:t,onChange:n}),l=e!==void 0,d=l?e:o;{const f=i.useRef(e!==void 0);i.useEffect(()=>{const p=f.current;p!==l&&console.warn(`${r} is changing from ${p?"controlled":"uncontrolled"} to ${l?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=l},[l,r])}const u=i.useCallback(f=>{if(l){const p=Cc(f)?f(e):f;p!==e&&c.current?.(p)}else a(f)},[l,e,a,c]);return[d,u]}function yc({defaultProp:e,onChange:t}){const[n,r]=i.useState(e),o=i.useRef(n),a=i.useRef(t);return wc(()=>{a.current=t},[t]),i.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}function Cc(e){return typeof e=="function"}function Nc(e,t=globalThis?.document){const n=ve(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Sc="DismissableLayer",on="dismissableLayer.update",Ec="dismissableLayer.pointerDownOutside",Rc="dismissableLayer.focusOutside",er,Wr=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),At=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:c,onDismiss:l,...d}=e,u=i.useContext(Wr),[f,p]=i.useState(null),g=f?.ownerDocument??globalThis?.document,[,v]=i.useState({}),h=q(t,N=>p(N)),m=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),b=m.indexOf(x),w=f?m.indexOf(f):-1,y=u.layersWithOutsidePointerEventsDisabled.size>0,C=w>=b,S=Mc(N=>{const D=N.target,E=[...u.branches].some(T=>T.contains(D));!C||E||(o?.(N),c?.(N),N.defaultPrevented||l?.())},g),R=Dc(N=>{const D=N.target;[...u.branches].some(T=>T.contains(D))||(a?.(N),c?.(N),N.defaultPrevented||l?.())},g);return Nc(N=>{w===u.layers.size-1&&(r?.(N),!N.defaultPrevented&&l&&(N.preventDefault(),l()))},g),i.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(er=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),tr(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=er)}},[f,g,n,u]),i.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),tr())},[f,u]),i.useEffect(()=>{const N=()=>v({});return document.addEventListener(on,N),()=>document.removeEventListener(on,N)},[]),s.jsx(Y.div,{...d,ref:h,style:{pointerEvents:y?C?"auto":"none":void 0,...e.style},onFocusCapture:L(e.onFocusCapture,R.onFocusCapture),onBlurCapture:L(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:L(e.onPointerDownCapture,S.onPointerDownCapture)})});At.displayName=Sc;var jc="DismissableLayerBranch",Ac=i.forwardRef((e,t)=>{const n=i.useContext(Wr),r=i.useRef(null),o=q(t,r);return i.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),s.jsx(Y.div,{...e,ref:o})});Ac.displayName=jc;function Mc(e,t=globalThis?.document){const n=ve(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const a=l=>{if(l.target&&!r.current){let d=function(){Vr(Ec,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=d,t.addEventListener("click",o.current,{once:!0})):d()}else t.removeEventListener("click",o.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Dc(e,t=globalThis?.document){const n=ve(e),r=i.useRef(!1);return i.useEffect(()=>{const o=a=>{a.target&&!r.current&&Vr(Rc,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function tr(){const e=new CustomEvent(on);document.dispatchEvent(e)}function Vr(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Pr(o,a):o.dispatchEvent(a)}var Vt="focusScope.autoFocusOnMount",Gt="focusScope.autoFocusOnUnmount",nr={bubbles:!1,cancelable:!0},Pc="FocusScope",wn=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[l,d]=i.useState(null),u=ve(o),f=ve(a),p=i.useRef(null),g=q(t,m=>d(m)),v=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let m=function(y){if(v.paused||!l)return;const C=y.target;l.contains(C)?p.current=C:Ce(p.current,{select:!0})},x=function(y){if(v.paused||!l)return;const C=y.relatedTarget;C!==null&&(l.contains(C)||Ce(p.current,{select:!0}))},b=function(y){if(document.activeElement===document.body)for(const S of y)S.removedNodes.length>0&&Ce(l)};document.addEventListener("focusin",m),document.addEventListener("focusout",x);const w=new MutationObserver(b);return l&&w.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",x),w.disconnect()}}},[r,l,v.paused]),i.useEffect(()=>{if(l){or.add(v);const m=document.activeElement;if(!l.contains(m)){const b=new CustomEvent(Vt,nr);l.addEventListener(Vt,u),l.dispatchEvent(b),b.defaultPrevented||(_c(Lc(Gr(l)),{select:!0}),document.activeElement===m&&Ce(l))}return()=>{l.removeEventListener(Vt,u),setTimeout(()=>{const b=new CustomEvent(Gt,nr);l.addEventListener(Gt,f),l.dispatchEvent(b),b.defaultPrevented||Ce(m??document.body,{select:!0}),l.removeEventListener(Gt,f),or.remove(v)},0)}}},[l,u,f,v]);const h=i.useCallback(m=>{if(!n&&!r||v.paused)return;const x=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,b=document.activeElement;if(x&&b){const w=m.currentTarget,[y,C]=Tc(w);y&&C?!m.shiftKey&&b===C?(m.preventDefault(),n&&Ce(y,{select:!0})):m.shiftKey&&b===y&&(m.preventDefault(),n&&Ce(C,{select:!0})):b===w&&m.preventDefault()}},[n,r,v.paused]);return s.jsx(Y.div,{tabIndex:-1,...c,ref:g,onKeyDown:h})});wn.displayName=Pc;function _c(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Ce(r,{select:t}),document.activeElement!==n)return}function Tc(e){const t=Gr(e),n=rr(t,e),r=rr(t.reverse(),e);return[n,r]}function Gr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function rr(e,t){for(const n of e)if(!Ic(n,{upTo:t}))return n}function Ic(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Oc(e){return e instanceof HTMLInputElement&&"select"in e}function Ce(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Oc(e)&&t&&e.select()}}var or=kc();function kc(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=sr(e,t),e.unshift(t)},remove(t){e=sr(e,t),e[0]?.resume()}}}function sr(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Lc(e){return e.filter(t=>t.tagName!=="A")}var Fc="Portal",yn=i.forwardRef((e,t)=>{const{container:n,...r}=e,[o,a]=i.useState(!1);Se(()=>a(!0),[]);const c=n||o&&globalThis?.document?.body;return c?Ua.createPortal(s.jsx(Y.div,{...r,ref:t}),c):null});yn.displayName=Fc;function $c(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var we=e=>{const{present:t,children:n}=e,r=Bc(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),a=q(r.ref,zc(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:a}):null};we.displayName="Presence";function Bc(e){const[t,n]=i.useState(),r=i.useRef(null),o=i.useRef(e),a=i.useRef("none"),c=e?"mounted":"unmounted",[l,d]=$c(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=at(r.current);a.current=l==="mounted"?u:"none"},[l]),Se(()=>{const u=r.current,f=o.current;if(f!==e){const g=a.current,v=at(u);e?d("MOUNT"):v==="none"||u?.display==="none"?d("UNMOUNT"):d(f&&g!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,d]),Se(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,p=v=>{const m=at(r.current).includes(v.animationName);if(v.target===t&&m&&(d("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},g=v=>{v.target===t&&(a.current=at(r.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else d("ANIMATION_END")},[t,d]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:i.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function at(e){return e?.animationName||"none"}function zc(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Kt=0;function Kr(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ar()),document.body.insertAdjacentElement("beforeend",e[1]??ar()),Kt++,()=>{Kt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Kt--}},[])}function ar(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var fe=function(){return fe=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},fe.apply(this,arguments)};function Yr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Uc(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var pt="right-scroll-bar-position",mt="width-before-scroll-bar",Hc="with-scroll-bars-hidden",Wc="--removed-body-scroll-bar-size";function Yt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Vc(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Gc=typeof window<"u"?i.useLayoutEffect:i.useEffect,ir=new WeakMap;function Kc(e,t){var n=Vc(null,function(r){return e.forEach(function(o){return Yt(o,r)})});return Gc(function(){var r=ir.get(n);if(r){var o=new Set(r),a=new Set(e),c=n.current;o.forEach(function(l){a.has(l)||Yt(l,null)}),a.forEach(function(l){o.has(l)||Yt(l,c)})}ir.set(n,e)},[e]),n}function Yc(e){return e}function qc(e,t){t===void 0&&(t=Yc);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var c=t(a,r);return n.push(c),function(){n=n.filter(function(l){return l!==c})}},assignSyncMedium:function(a){for(r=!0;n.length;){var c=n;n=[],c.forEach(a)}n={push:function(l){return a(l)},filter:function(){return n}}},assignMedium:function(a){r=!0;var c=[];if(n.length){var l=n;n=[],l.forEach(a),c=n}var d=function(){var f=c;c=[],f.forEach(a)},u=function(){return Promise.resolve().then(d)};u(),n={push:function(f){c.push(f),u()},filter:function(f){return c=c.filter(f),n}}}};return o}function Xc(e){e===void 0&&(e={});var t=qc(null);return t.options=fe({async:!0,ssr:!1},e),t}var qr=function(e){var t=e.sideCar,n=Yr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return i.createElement(r,fe({},n))};qr.isSideCarExport=!0;function Zc(e,t){return e.useMedium(t),qr}var Xr=Xc(),qt=function(){},Mt=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:qt,onWheelCapture:qt,onTouchMoveCapture:qt}),o=r[0],a=r[1],c=e.forwardProps,l=e.children,d=e.className,u=e.removeScrollBar,f=e.enabled,p=e.shards,g=e.sideCar,v=e.noRelative,h=e.noIsolation,m=e.inert,x=e.allowPinchZoom,b=e.as,w=b===void 0?"div":b,y=e.gapMode,C=Yr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=g,R=Kc([n,t]),N=fe(fe({},C),o);return i.createElement(i.Fragment,null,f&&i.createElement(S,{sideCar:Xr,removeScrollBar:u,shards:p,noRelative:v,noIsolation:h,inert:m,setCallbacks:a,allowPinchZoom:!!x,lockRef:n,gapMode:y}),c?i.cloneElement(i.Children.only(l),fe(fe({},N),{ref:R})):i.createElement(w,fe({},N,{className:d,ref:R}),l))});Mt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Mt.classNames={fullWidth:mt,zeroRight:pt};var Jc=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Qc(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Jc();return t&&e.setAttribute("nonce",t),e}function el(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function tl(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var nl=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Qc())&&(el(t,n),tl(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},rl=function(){var e=nl();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Zr=function(){var e=rl(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},ol={left:0,top:0,right:0,gap:0},Xt=function(e){return parseInt(e||"",10)||0},sl=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Xt(n),Xt(r),Xt(o)]},al=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return ol;var t=sl(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},il=Zr(),ke="data-scroll-locked",cl=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Hc,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(ke,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(pt,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(mt,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(pt," .").concat(pt,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(mt," .").concat(mt,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ke,`] {
    `).concat(Wc,": ").concat(l,`px;
  }
`)},cr=function(){var e=parseInt(document.body.getAttribute(ke)||"0",10);return isFinite(e)?e:0},ll=function(){i.useEffect(function(){return document.body.setAttribute(ke,(cr()+1).toString()),function(){var e=cr()-1;e<=0?document.body.removeAttribute(ke):document.body.setAttribute(ke,e.toString())}},[])},dl=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;ll();var a=i.useMemo(function(){return al(o)},[o]);return i.createElement(il,{styles:cl(a,!t,o,n?"":"!important")})},sn=!1;if(typeof window<"u")try{var it=Object.defineProperty({},"passive",{get:function(){return sn=!0,!0}});window.addEventListener("test",it,it),window.removeEventListener("test",it,it)}catch{sn=!1}var Te=sn?{passive:!1}:!1,ul=function(e){return e.tagName==="TEXTAREA"},Jr=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!ul(e)&&n[t]==="visible")},fl=function(e){return Jr(e,"overflowY")},pl=function(e){return Jr(e,"overflowX")},lr=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Qr(e,r);if(o){var a=eo(e,r),c=a[1],l=a[2];if(c>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ml=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},hl=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Qr=function(e,t){return e==="v"?fl(t):pl(t)},eo=function(e,t){return e==="v"?ml(t):hl(t)},gl=function(e,t){return e==="h"&&t==="rtl"?-1:1},vl=function(e,t,n,r,o){var a=gl(e,window.getComputedStyle(t).direction),c=a*r,l=n.target,d=t.contains(l),u=!1,f=c>0,p=0,g=0;do{if(!l)break;var v=eo(e,l),h=v[0],m=v[1],x=v[2],b=m-x-a*h;(h||b)&&Qr(e,l)&&(p+=b,g+=h);var w=l.parentNode;l=w&&w.nodeType===Node.DOCUMENT_FRAGMENT_NODE?w.host:w}while(!d&&l!==document.body||d&&(t.contains(l)||t===l));return(f&&Math.abs(p)<1||!f&&Math.abs(g)<1)&&(u=!0),u},ct=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},dr=function(e){return[e.deltaX,e.deltaY]},ur=function(e){return e&&"current"in e?e.current:e},xl=function(e,t){return e[0]===t[0]&&e[1]===t[1]},bl=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},wl=0,Ie=[];function yl(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(wl++)[0],a=i.useState(Zr)[0],c=i.useRef(e);i.useEffect(function(){c.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=Uc([e.lockRef.current],(e.shards||[]).map(ur),!0).filter(Boolean);return m.forEach(function(x){return x.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(m,x){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!c.current.allowPinchZoom;var b=ct(m),w=n.current,y="deltaX"in m?m.deltaX:w[0]-b[0],C="deltaY"in m?m.deltaY:w[1]-b[1],S,R=m.target,N=Math.abs(y)>Math.abs(C)?"h":"v";if("touches"in m&&N==="h"&&R.type==="range")return!1;var D=lr(N,R);if(!D)return!0;if(D?S=N:(S=N==="v"?"h":"v",D=lr(N,R)),!D)return!1;if(!r.current&&"changedTouches"in m&&(y||C)&&(r.current=S),!S)return!0;var E=r.current||S;return vl(E,x,m,E==="h"?y:C)},[]),d=i.useCallback(function(m){var x=m;if(!(!Ie.length||Ie[Ie.length-1]!==a)){var b="deltaY"in x?dr(x):ct(x),w=t.current.filter(function(S){return S.name===x.type&&(S.target===x.target||x.target===S.shadowParent)&&xl(S.delta,b)})[0];if(w&&w.should){x.cancelable&&x.preventDefault();return}if(!w){var y=(c.current.shards||[]).map(ur).filter(Boolean).filter(function(S){return S.contains(x.target)}),C=y.length>0?l(x,y[0]):!c.current.noIsolation;C&&x.cancelable&&x.preventDefault()}}},[]),u=i.useCallback(function(m,x,b,w){var y={name:m,delta:x,target:b,should:w,shadowParent:Cl(b)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(C){return C!==y})},1)},[]),f=i.useCallback(function(m){n.current=ct(m),r.current=void 0},[]),p=i.useCallback(function(m){u(m.type,dr(m),m.target,l(m,e.lockRef.current))},[]),g=i.useCallback(function(m){u(m.type,ct(m),m.target,l(m,e.lockRef.current))},[]);i.useEffect(function(){return Ie.push(a),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:g}),document.addEventListener("wheel",d,Te),document.addEventListener("touchmove",d,Te),document.addEventListener("touchstart",f,Te),function(){Ie=Ie.filter(function(m){return m!==a}),document.removeEventListener("wheel",d,Te),document.removeEventListener("touchmove",d,Te),document.removeEventListener("touchstart",f,Te)}},[]);var v=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(a,{styles:bl(o)}):null,v?i.createElement(dl,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Cl(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Nl=Zc(Xr,yl);var Cn=i.forwardRef(function(e,t){return i.createElement(Mt,fe({},e,{ref:t,sideCar:Nl}))});Cn.classNames=Mt.classNames;var Sl=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Oe=new WeakMap,lt=new WeakMap,dt={},Zt=0,to=function(e){return e&&(e.host||to(e.parentNode))},El=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=to(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Rl=function(e,t,n,r){var o=El(t,Array.isArray(e)?e:[e]);dt[n]||(dt[n]=new WeakMap);var a=dt[n],c=[],l=new Set,d=new Set(o),u=function(p){!p||l.has(p)||(l.add(p),u(p.parentNode))};o.forEach(u);var f=function(p){!p||d.has(p)||Array.prototype.forEach.call(p.children,function(g){if(l.has(g))f(g);else try{var v=g.getAttribute(r),h=v!==null&&v!=="false",m=(Oe.get(g)||0)+1,x=(a.get(g)||0)+1;Oe.set(g,m),a.set(g,x),c.push(g),m===1&&h&&lt.set(g,!0),x===1&&g.setAttribute(n,"true"),h||g.setAttribute(r,"true")}catch(b){console.error("aria-hidden: cannot operate on ",g,b)}})};return f(t),l.clear(),Zt++,function(){c.forEach(function(p){var g=Oe.get(p)-1,v=a.get(p)-1;Oe.set(p,g),a.set(p,v),g||(lt.has(p)||p.removeAttribute(r),lt.delete(p)),v||p.removeAttribute(n)}),Zt--,Zt||(Oe=new WeakMap,Oe=new WeakMap,lt=new WeakMap,dt={})}},no=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Sl(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Rl(r,o,n,"aria-hidden")):function(){return null}},Dt="Dialog",[ro,oo]=je(Dt),[jl,le]=ro(Dt),so=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,l=i.useRef(null),d=i.useRef(null),[u,f]=jt({prop:r,defaultProp:o??!1,onChange:a,caller:Dt});return s.jsx(jl,{scope:t,triggerRef:l,contentRef:d,contentId:Ae(),titleId:Ae(),descriptionId:Ae(),open:u,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(p=>!p),[f]),modal:c,children:n})};so.displayName=Dt;var ao="DialogTrigger",io=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=le(ao,n),a=q(t,o.triggerRef);return s.jsx(Y.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":En(o.open),...r,ref:a,onClick:L(e.onClick,o.onOpenToggle)})});io.displayName=ao;var Nn="DialogPortal",[Al,co]=ro(Nn,{forceMount:void 0}),lo=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=le(Nn,t);return s.jsx(Al,{scope:t,forceMount:n,children:i.Children.map(r,c=>s.jsx(we,{present:n||a.open,children:s.jsx(yn,{asChild:!0,container:o,children:c})}))})};lo.displayName=Nn;var xt="DialogOverlay",uo=i.forwardRef((e,t)=>{const n=co(xt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=le(xt,e.__scopeDialog);return a.modal?s.jsx(we,{present:r||a.open,children:s.jsx(Dl,{...o,ref:t})}):null});uo.displayName=xt;var Ml=gt("DialogOverlay.RemoveScroll"),Dl=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=le(xt,n);return s.jsx(Cn,{as:Ml,allowPinchZoom:!0,shards:[o.contentRef],children:s.jsx(Y.div,{"data-state":En(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Me="DialogContent",fo=i.forwardRef((e,t)=>{const n=co(Me,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=le(Me,e.__scopeDialog);return s.jsx(we,{present:r||a.open,children:a.modal?s.jsx(Pl,{...o,ref:t}):s.jsx(_l,{...o,ref:t})})});fo.displayName=Me;var Pl=i.forwardRef((e,t)=>{const n=le(Me,e.__scopeDialog),r=i.useRef(null),o=q(t,n.contentRef,r);return i.useEffect(()=>{const a=r.current;if(a)return no(a)},[]),s.jsx(po,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:L(e.onCloseAutoFocus,a=>{a.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:L(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0;(c.button===2||l)&&a.preventDefault()}),onFocusOutside:L(e.onFocusOutside,a=>a.preventDefault())})}),_l=i.forwardRef((e,t)=>{const n=le(Me,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return s.jsx(po,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||n.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=a.target;n.triggerRef.current?.contains(c)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),po=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...c}=e,l=le(Me,n),d=i.useRef(null),u=q(t,d);return Kr(),s.jsxs(s.Fragment,{children:[s.jsx(wn,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:s.jsx(At,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":En(l.open),...c,ref:u,onDismiss:()=>l.onOpenChange(!1)})}),s.jsxs(s.Fragment,{children:[s.jsx(Il,{titleId:l.titleId}),s.jsx(kl,{contentRef:d,descriptionId:l.descriptionId})]})]})}),Sn="DialogTitle",mo=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=le(Sn,n);return s.jsx(Y.h2,{id:o.titleId,...r,ref:t})});mo.displayName=Sn;var ho="DialogDescription",go=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=le(ho,n);return s.jsx(Y.p,{id:o.descriptionId,...r,ref:t})});go.displayName=ho;var vo="DialogClose",xo=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=le(vo,n);return s.jsx(Y.button,{type:"button",...r,ref:t,onClick:L(e.onClick,()=>o.onOpenChange(!1))})});xo.displayName=vo;function En(e){return e?"open":"closed"}var bo="DialogTitleWarning",[Tl,wo]=Qa(bo,{contentName:Me,titleName:Sn,docsSlug:"dialog"}),Il=({titleId:e})=>{const t=wo(bo),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Ol="DialogDescriptionWarning",kl=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${wo(Ol).contentName}}.`;return i.useEffect(()=>{const o=e.current?.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},yo=so,Ll=io,Co=lo,Rn=uo,jn=fo,An=mo,Mn=go,Dn=xo;const Fl=yo,$l=Co,No=i.forwardRef(({className:e,...t},n)=>s.jsx(Rn,{className:M("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));No.displayName=Rn.displayName;const Bl=vn("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),So=i.forwardRef(({side:e="right",className:t,children:n,...r},o)=>s.jsxs($l,{children:[s.jsx(No,{}),s.jsxs(jn,{ref:o,className:M(Bl({side:e}),t),...r,children:[n,s.jsxs(Dn,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[s.jsx(Et,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));So.displayName=jn.displayName;const zl=i.forwardRef(({className:e,...t},n)=>s.jsx(An,{ref:n,className:M("text-lg font-semibold text-foreground",e),...t}));zl.displayName=An.displayName;const Ul=i.forwardRef(({className:e,...t},n)=>s.jsx(Mn,{ref:n,className:M("text-sm text-muted-foreground",e),...t}));Ul.displayName=Mn.displayName;function fr({className:e,...t}){return s.jsx("div",{className:M("animate-pulse rounded-md bg-muted",e),...t})}const Hl=["top","right","bottom","left"],Ee=Math.min,re=Math.max,bt=Math.round,ut=Math.floor,me=e=>({x:e,y:e}),Wl={left:"right",right:"left",bottom:"top",top:"bottom"},Vl={start:"end",end:"start"};function an(e,t,n){return re(e,Ee(t,n))}function xe(e,t){return typeof e=="function"?e(t):e}function be(e){return e.split("-")[0]}function ze(e){return e.split("-")[1]}function Pn(e){return e==="x"?"y":"x"}function _n(e){return e==="y"?"height":"width"}const Gl=new Set(["top","bottom"]);function pe(e){return Gl.has(be(e))?"y":"x"}function Tn(e){return Pn(pe(e))}function Kl(e,t,n){n===void 0&&(n=!1);const r=ze(e),o=Tn(e),a=_n(o);let c=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(c=wt(c)),[c,wt(c)]}function Yl(e){const t=wt(e);return[cn(e),t,cn(t)]}function cn(e){return e.replace(/start|end/g,t=>Vl[t])}const pr=["left","right"],mr=["right","left"],ql=["top","bottom"],Xl=["bottom","top"];function Zl(e,t,n){switch(e){case"top":case"bottom":return n?t?mr:pr:t?pr:mr;case"left":case"right":return t?ql:Xl;default:return[]}}function Jl(e,t,n,r){const o=ze(e);let a=Zl(be(e),n==="start",r);return o&&(a=a.map(c=>c+"-"+o),t&&(a=a.concat(a.map(cn)))),a}function wt(e){return e.replace(/left|right|bottom|top/g,t=>Wl[t])}function Ql(e){return{top:0,right:0,bottom:0,left:0,...e}}function Eo(e){return typeof e!="number"?Ql(e):{top:e,right:e,bottom:e,left:e}}function yt(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function hr(e,t,n){let{reference:r,floating:o}=e;const a=pe(t),c=Tn(t),l=_n(c),d=be(t),u=a==="y",f=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,g=r[l]/2-o[l]/2;let v;switch(d){case"top":v={x:f,y:r.y-o.height};break;case"bottom":v={x:f,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:p};break;case"left":v={x:r.x-o.width,y:p};break;default:v={x:r.x,y:r.y}}switch(ze(t)){case"start":v[c]-=g*(n&&u?-1:1);break;case"end":v[c]+=g*(n&&u?-1:1);break}return v}const ed=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:c}=n,l=a.filter(Boolean),d=await(c.isRTL==null?void 0:c.isRTL(t));let u=await c.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:p}=hr(u,r,d),g=r,v={},h=0;for(let m=0;m<l.length;m++){const{name:x,fn:b}=l[m],{x:w,y,data:C,reset:S}=await b({x:f,y:p,initialPlacement:r,placement:g,strategy:o,middlewareData:v,rects:u,platform:c,elements:{reference:e,floating:t}});f=w??f,p=y??p,v={...v,[x]:{...v[x],...C}},S&&h<=50&&(h++,typeof S=="object"&&(S.placement&&(g=S.placement),S.rects&&(u=S.rects===!0?await c.getElementRects({reference:e,floating:t,strategy:o}):S.rects),{x:f,y:p}=hr(u,g,d)),m=-1)}return{x:f,y:p,placement:g,strategy:o,middlewareData:v}};async function Ge(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:a,rects:c,elements:l,strategy:d}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:g=!1,padding:v=0}=xe(t,e),h=Eo(v),x=l[g?p==="floating"?"reference":"floating":p],b=yt(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(x)))==null||n?x:x.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(l.floating)),boundary:u,rootBoundary:f,strategy:d})),w=p==="floating"?{x:r,y:o,width:c.floating.width,height:c.floating.height}:c.reference,y=await(a.getOffsetParent==null?void 0:a.getOffsetParent(l.floating)),C=await(a.isElement==null?void 0:a.isElement(y))?await(a.getScale==null?void 0:a.getScale(y))||{x:1,y:1}:{x:1,y:1},S=yt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:w,offsetParent:y,strategy:d}):w);return{top:(b.top-S.top+h.top)/C.y,bottom:(S.bottom-b.bottom+h.bottom)/C.y,left:(b.left-S.left+h.left)/C.x,right:(S.right-b.right+h.right)/C.x}}const td=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:c,elements:l,middlewareData:d}=t,{element:u,padding:f=0}=xe(e,t)||{};if(u==null)return{};const p=Eo(f),g={x:n,y:r},v=Tn(o),h=_n(v),m=await c.getDimensions(u),x=v==="y",b=x?"top":"left",w=x?"bottom":"right",y=x?"clientHeight":"clientWidth",C=a.reference[h]+a.reference[v]-g[v]-a.floating[h],S=g[v]-a.reference[v],R=await(c.getOffsetParent==null?void 0:c.getOffsetParent(u));let N=R?R[y]:0;(!N||!await(c.isElement==null?void 0:c.isElement(R)))&&(N=l.floating[y]||a.floating[h]);const D=C/2-S/2,E=N/2-m[h]/2-1,T=Ee(p[b],E),O=Ee(p[w],E),W=T,H=N-m[h]-O,U=N/2-m[h]/2+D,G=an(W,U,H),z=!d.arrow&&ze(o)!=null&&U!==G&&a.reference[h]/2-(U<W?T:O)-m[h]/2<0,$=z?U<W?U-W:U-H:0;return{[v]:g[v]+$,data:{[v]:G,centerOffset:U-G-$,...z&&{alignmentOffset:$}},reset:z}}}),nd=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:c,initialPlacement:l,platform:d,elements:u}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:g,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:m=!0,...x}=xe(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const b=be(o),w=pe(l),y=be(l)===l,C=await(d.isRTL==null?void 0:d.isRTL(u.floating)),S=g||(y||!m?[wt(l)]:Yl(l)),R=h!=="none";!g&&R&&S.push(...Jl(l,m,h,C));const N=[l,...S],D=await Ge(t,x),E=[];let T=((r=a.flip)==null?void 0:r.overflows)||[];if(f&&E.push(D[b]),p){const U=Kl(o,c,C);E.push(D[U[0]],D[U[1]])}if(T=[...T,{placement:o,overflows:E}],!E.every(U=>U<=0)){var O,W;const U=(((O=a.flip)==null?void 0:O.index)||0)+1,G=N[U];if(G&&(!(p==="alignment"?w!==pe(G):!1)||T.every(k=>k.overflows[0]>0&&pe(k.placement)===w)))return{data:{index:U,overflows:T},reset:{placement:G}};let z=(W=T.filter($=>$.overflows[0]<=0).sort(($,k)=>$.overflows[1]-k.overflows[1])[0])==null?void 0:W.placement;if(!z)switch(v){case"bestFit":{var H;const $=(H=T.filter(k=>{if(R){const A=pe(k.placement);return A===w||A==="y"}return!0}).map(k=>[k.placement,k.overflows.filter(A=>A>0).reduce((A,P)=>A+P,0)]).sort((k,A)=>k[1]-A[1])[0])==null?void 0:H[0];$&&(z=$);break}case"initialPlacement":z=l;break}if(o!==z)return{reset:{placement:z}}}return{}}}};function gr(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function vr(e){return Hl.some(t=>e[t]>=0)}const rd=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=xe(e,t);switch(r){case"referenceHidden":{const a=await Ge(t,{...o,elementContext:"reference"}),c=gr(a,n.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:vr(c)}}}case"escaped":{const a=await Ge(t,{...o,altBoundary:!0}),c=gr(a,n.floating);return{data:{escapedOffsets:c,escaped:vr(c)}}}default:return{}}}}},Ro=new Set(["left","top"]);async function od(e,t){const{placement:n,platform:r,elements:o}=e,a=await(r.isRTL==null?void 0:r.isRTL(o.floating)),c=be(n),l=ze(n),d=pe(n)==="y",u=Ro.has(c)?-1:1,f=a&&d?-1:1,p=xe(t,e);let{mainAxis:g,crossAxis:v,alignmentAxis:h}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return l&&typeof h=="number"&&(v=l==="end"?h*-1:h),d?{x:v*f,y:g*u}:{x:g*u,y:v*f}}const sd=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:c,middlewareData:l}=t,d=await od(t,e);return c===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+d.x,y:a+d.y,data:{...d,placement:c}}}}},ad=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:c=!1,limiter:l={fn:x=>{let{x:b,y:w}=x;return{x:b,y:w}}},...d}=xe(e,t),u={x:n,y:r},f=await Ge(t,d),p=pe(be(o)),g=Pn(p);let v=u[g],h=u[p];if(a){const x=g==="y"?"top":"left",b=g==="y"?"bottom":"right",w=v+f[x],y=v-f[b];v=an(w,v,y)}if(c){const x=p==="y"?"top":"left",b=p==="y"?"bottom":"right",w=h+f[x],y=h-f[b];h=an(w,h,y)}const m=l.fn({...t,[g]:v,[p]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[g]:a,[p]:c}}}}}},id=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:c}=t,{offset:l=0,mainAxis:d=!0,crossAxis:u=!0}=xe(e,t),f={x:n,y:r},p=pe(o),g=Pn(p);let v=f[g],h=f[p];const m=xe(l,t),x=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(d){const y=g==="y"?"height":"width",C=a.reference[g]-a.floating[y]+x.mainAxis,S=a.reference[g]+a.reference[y]-x.mainAxis;v<C?v=C:v>S&&(v=S)}if(u){var b,w;const y=g==="y"?"width":"height",C=Ro.has(be(o)),S=a.reference[p]-a.floating[y]+(C&&((b=c.offset)==null?void 0:b[p])||0)+(C?0:x.crossAxis),R=a.reference[p]+a.reference[y]+(C?0:((w=c.offset)==null?void 0:w[p])||0)-(C?x.crossAxis:0);h<S?h=S:h>R&&(h=R)}return{[g]:v,[p]:h}}}},cd=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:c,elements:l}=t,{apply:d=()=>{},...u}=xe(e,t),f=await Ge(t,u),p=be(o),g=ze(o),v=pe(o)==="y",{width:h,height:m}=a.floating;let x,b;p==="top"||p==="bottom"?(x=p,b=g===(await(c.isRTL==null?void 0:c.isRTL(l.floating))?"start":"end")?"left":"right"):(b=p,x=g==="end"?"top":"bottom");const w=m-f.top-f.bottom,y=h-f.left-f.right,C=Ee(m-f[x],w),S=Ee(h-f[b],y),R=!t.middlewareData.shift;let N=C,D=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(D=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=w),R&&!g){const T=re(f.left,0),O=re(f.right,0),W=re(f.top,0),H=re(f.bottom,0);v?D=h-2*(T!==0||O!==0?T+O:re(f.left,f.right)):N=m-2*(W!==0||H!==0?W+H:re(f.top,f.bottom))}await d({...t,availableWidth:D,availableHeight:N});const E=await c.getDimensions(l.floating);return h!==E.width||m!==E.height?{reset:{rects:!0}}:{}}}};function Pt(){return typeof window<"u"}function Ue(e){return jo(e)?(e.nodeName||"").toLowerCase():"#document"}function se(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ge(e){var t;return(t=(jo(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function jo(e){return Pt()?e instanceof Node||e instanceof se(e).Node:!1}function ie(e){return Pt()?e instanceof Element||e instanceof se(e).Element:!1}function he(e){return Pt()?e instanceof HTMLElement||e instanceof se(e).HTMLElement:!1}function xr(e){return!Pt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof se(e).ShadowRoot}const ld=new Set(["inline","contents"]);function Qe(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=ce(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ld.has(o)}const dd=new Set(["table","td","th"]);function ud(e){return dd.has(Ue(e))}const fd=[":popover-open",":modal"];function _t(e){return fd.some(t=>{try{return e.matches(t)}catch{return!1}})}const pd=["transform","translate","scale","rotate","perspective"],md=["transform","translate","scale","rotate","perspective","filter"],hd=["paint","layout","strict","content"];function In(e){const t=On(),n=ie(e)?ce(e):e;return pd.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||md.some(r=>(n.willChange||"").includes(r))||hd.some(r=>(n.contain||"").includes(r))}function gd(e){let t=Re(e);for(;he(t)&&!$e(t);){if(In(t))return t;if(_t(t))return null;t=Re(t)}return null}function On(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const vd=new Set(["html","body","#document"]);function $e(e){return vd.has(Ue(e))}function ce(e){return se(e).getComputedStyle(e)}function Tt(e){return ie(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Re(e){if(Ue(e)==="html")return e;const t=e.assignedSlot||e.parentNode||xr(e)&&e.host||ge(e);return xr(t)?t.host:t}function Ao(e){const t=Re(e);return $e(t)?e.ownerDocument?e.ownerDocument.body:e.body:he(t)&&Qe(t)?t:Ao(t)}function Ke(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Ao(e),a=o===((r=e.ownerDocument)==null?void 0:r.body),c=se(o);if(a){const l=ln(c);return t.concat(c,c.visualViewport||[],Qe(o)?o:[],l&&n?Ke(l):[])}return t.concat(o,Ke(o,[],n))}function ln(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Mo(e){const t=ce(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=he(e),a=o?e.offsetWidth:n,c=o?e.offsetHeight:r,l=bt(n)!==a||bt(r)!==c;return l&&(n=a,r=c),{width:n,height:r,$:l}}function kn(e){return ie(e)?e:e.contextElement}function Le(e){const t=kn(e);if(!he(t))return me(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=Mo(t);let c=(a?bt(n.width):n.width)/r,l=(a?bt(n.height):n.height)/o;return(!c||!Number.isFinite(c))&&(c=1),(!l||!Number.isFinite(l))&&(l=1),{x:c,y:l}}const xd=me(0);function Do(e){const t=se(e);return!On()||!t.visualViewport?xd:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function bd(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==se(e)?!1:t}function De(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),a=kn(e);let c=me(1);t&&(r?ie(r)&&(c=Le(r)):c=Le(e));const l=bd(a,n,r)?Do(a):me(0);let d=(o.left+l.x)/c.x,u=(o.top+l.y)/c.y,f=o.width/c.x,p=o.height/c.y;if(a){const g=se(a),v=r&&ie(r)?se(r):r;let h=g,m=ln(h);for(;m&&r&&v!==h;){const x=Le(m),b=m.getBoundingClientRect(),w=ce(m),y=b.left+(m.clientLeft+parseFloat(w.paddingLeft))*x.x,C=b.top+(m.clientTop+parseFloat(w.paddingTop))*x.y;d*=x.x,u*=x.y,f*=x.x,p*=x.y,d+=y,u+=C,h=se(m),m=ln(h)}}return yt({width:f,height:p,x:d,y:u})}function Ln(e,t){const n=Tt(e).scrollLeft;return t?t.left+n:De(ge(e)).left+n}function Po(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Ln(e,r)),a=r.top+t.scrollTop;return{x:o,y:a}}function wd(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a=o==="fixed",c=ge(r),l=t?_t(t.floating):!1;if(r===c||l&&a)return n;let d={scrollLeft:0,scrollTop:0},u=me(1);const f=me(0),p=he(r);if((p||!p&&!a)&&((Ue(r)!=="body"||Qe(c))&&(d=Tt(r)),he(r))){const v=De(r);u=Le(r),f.x=v.x+r.clientLeft,f.y=v.y+r.clientTop}const g=c&&!p&&!a?Po(c,d,!0):me(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-d.scrollLeft*u.x+f.x+g.x,y:n.y*u.y-d.scrollTop*u.y+f.y+g.y}}function yd(e){return Array.from(e.getClientRects())}function Cd(e){const t=ge(e),n=Tt(e),r=e.ownerDocument.body,o=re(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=re(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let c=-n.scrollLeft+Ln(e);const l=-n.scrollTop;return ce(r).direction==="rtl"&&(c+=re(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:c,y:l}}function Nd(e,t){const n=se(e),r=ge(e),o=n.visualViewport;let a=r.clientWidth,c=r.clientHeight,l=0,d=0;if(o){a=o.width,c=o.height;const u=On();(!u||u&&t==="fixed")&&(l=o.offsetLeft,d=o.offsetTop)}return{width:a,height:c,x:l,y:d}}const Sd=new Set(["absolute","fixed"]);function Ed(e,t){const n=De(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=he(e)?Le(e):me(1),c=e.clientWidth*a.x,l=e.clientHeight*a.y,d=o*a.x,u=r*a.y;return{width:c,height:l,x:d,y:u}}function br(e,t,n){let r;if(t==="viewport")r=Nd(e,n);else if(t==="document")r=Cd(ge(e));else if(ie(t))r=Ed(t,n);else{const o=Do(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return yt(r)}function _o(e,t){const n=Re(e);return n===t||!ie(n)||$e(n)?!1:ce(n).position==="fixed"||_o(n,t)}function Rd(e,t){const n=t.get(e);if(n)return n;let r=Ke(e,[],!1).filter(l=>ie(l)&&Ue(l)!=="body"),o=null;const a=ce(e).position==="fixed";let c=a?Re(e):e;for(;ie(c)&&!$e(c);){const l=ce(c),d=In(c);!d&&l.position==="fixed"&&(o=null),(a?!d&&!o:!d&&l.position==="static"&&!!o&&Sd.has(o.position)||Qe(c)&&!d&&_o(e,c))?r=r.filter(f=>f!==c):o=l,c=Re(c)}return t.set(e,r),r}function jd(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const c=[...n==="clippingAncestors"?_t(t)?[]:Rd(t,this._c):[].concat(n),r],l=c[0],d=c.reduce((u,f)=>{const p=br(t,f,o);return u.top=re(p.top,u.top),u.right=Ee(p.right,u.right),u.bottom=Ee(p.bottom,u.bottom),u.left=re(p.left,u.left),u},br(t,l,o));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function Ad(e){const{width:t,height:n}=Mo(e);return{width:t,height:n}}function Md(e,t,n){const r=he(t),o=ge(t),a=n==="fixed",c=De(e,!0,a,t);let l={scrollLeft:0,scrollTop:0};const d=me(0);function u(){d.x=Ln(o)}if(r||!r&&!a)if((Ue(t)!=="body"||Qe(o))&&(l=Tt(t)),r){const v=De(t,!0,a,t);d.x=v.x+t.clientLeft,d.y=v.y+t.clientTop}else o&&u();a&&!r&&o&&u();const f=o&&!r&&!a?Po(o,l):me(0),p=c.left+l.scrollLeft-d.x-f.x,g=c.top+l.scrollTop-d.y-f.y;return{x:p,y:g,width:c.width,height:c.height}}function Jt(e){return ce(e).position==="static"}function wr(e,t){if(!he(e)||ce(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ge(e)===n&&(n=n.ownerDocument.body),n}function To(e,t){const n=se(e);if(_t(e))return n;if(!he(e)){let o=Re(e);for(;o&&!$e(o);){if(ie(o)&&!Jt(o))return o;o=Re(o)}return n}let r=wr(e,t);for(;r&&ud(r)&&Jt(r);)r=wr(r,t);return r&&$e(r)&&Jt(r)&&!In(r)?n:r||gd(e)||n}const Dd=async function(e){const t=this.getOffsetParent||To,n=this.getDimensions,r=await n(e.floating);return{reference:Md(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Pd(e){return ce(e).direction==="rtl"}const _d={convertOffsetParentRelativeRectToViewportRelativeRect:wd,getDocumentElement:ge,getClippingRect:jd,getOffsetParent:To,getElementRects:Dd,getClientRects:yd,getDimensions:Ad,getScale:Le,isElement:ie,isRTL:Pd};function Io(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Td(e,t){let n=null,r;const o=ge(e);function a(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function c(l,d){l===void 0&&(l=!1),d===void 0&&(d=1),a();const u=e.getBoundingClientRect(),{left:f,top:p,width:g,height:v}=u;if(l||t(),!g||!v)return;const h=ut(p),m=ut(o.clientWidth-(f+g)),x=ut(o.clientHeight-(p+v)),b=ut(f),y={rootMargin:-h+"px "+-m+"px "+-x+"px "+-b+"px",threshold:re(0,Ee(1,d))||1};let C=!0;function S(R){const N=R[0].intersectionRatio;if(N!==d){if(!C)return c();N?c(!1,N):r=setTimeout(()=>{c(!1,1e-7)},1e3)}N===1&&!Io(u,e.getBoundingClientRect())&&c(),C=!1}try{n=new IntersectionObserver(S,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(S,y)}n.observe(e)}return c(!0),a}function Id(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:d=!1}=r,u=kn(e),f=o||a?[...u?Ke(u):[],...Ke(t)]:[];f.forEach(b=>{o&&b.addEventListener("scroll",n,{passive:!0}),a&&b.addEventListener("resize",n)});const p=u&&l?Td(u,n):null;let g=-1,v=null;c&&(v=new ResizeObserver(b=>{let[w]=b;w&&w.target===u&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var y;(y=v)==null||y.observe(t)})),n()}),u&&!d&&v.observe(u),v.observe(t));let h,m=d?De(e):null;d&&x();function x(){const b=De(e);m&&!Io(m,b)&&n(),m=b,h=requestAnimationFrame(x)}return n(),()=>{var b;f.forEach(w=>{o&&w.removeEventListener("scroll",n),a&&w.removeEventListener("resize",n)}),p?.(),(b=v)==null||b.disconnect(),v=null,d&&cancelAnimationFrame(h)}}const Od=sd,kd=ad,Ld=nd,Fd=cd,$d=rd,yr=td,Bd=id,zd=(e,t,n)=>{const r=new Map,o={platform:_d,...n},a={...o.platform,_c:r};return ed(e,t,{...o,platform:a})};var Ud=typeof document<"u",Hd=function(){},ht=Ud?i.useLayoutEffect:Hd;function Ct(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ct(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const a=o[r];if(!(a==="_owner"&&e.$$typeof)&&!Ct(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function Oo(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Cr(e,t){const n=Oo(e);return Math.round(t*n)/n}function Qt(e){const t=i.useRef(e);return ht(()=>{t.current=e}),t}function Wd(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:c}={},transform:l=!0,whileElementsMounted:d,open:u}=e,[f,p]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[g,v]=i.useState(r);Ct(g,r)||v(r);const[h,m]=i.useState(null),[x,b]=i.useState(null),w=i.useCallback(k=>{k!==R.current&&(R.current=k,m(k))},[]),y=i.useCallback(k=>{k!==N.current&&(N.current=k,b(k))},[]),C=a||h,S=c||x,R=i.useRef(null),N=i.useRef(null),D=i.useRef(f),E=d!=null,T=Qt(d),O=Qt(o),W=Qt(u),H=i.useCallback(()=>{if(!R.current||!N.current)return;const k={placement:t,strategy:n,middleware:g};O.current&&(k.platform=O.current),zd(R.current,N.current,k).then(A=>{const P={...A,isPositioned:W.current!==!1};U.current&&!Ct(D.current,P)&&(D.current=P,Ha.flushSync(()=>{p(P)}))})},[g,t,n,O,W]);ht(()=>{u===!1&&D.current.isPositioned&&(D.current.isPositioned=!1,p(k=>({...k,isPositioned:!1})))},[u]);const U=i.useRef(!1);ht(()=>(U.current=!0,()=>{U.current=!1}),[]),ht(()=>{if(C&&(R.current=C),S&&(N.current=S),C&&S){if(T.current)return T.current(C,S,H);H()}},[C,S,H,T,E]);const G=i.useMemo(()=>({reference:R,floating:N,setReference:w,setFloating:y}),[w,y]),z=i.useMemo(()=>({reference:C,floating:S}),[C,S]),$=i.useMemo(()=>{const k={position:n,left:0,top:0};if(!z.floating)return k;const A=Cr(z.floating,f.x),P=Cr(z.floating,f.y);return l?{...k,transform:"translate("+A+"px, "+P+"px)",...Oo(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:A,top:P}},[n,l,z.floating,f.x,f.y]);return i.useMemo(()=>({...f,update:H,refs:G,elements:z,floatingStyles:$}),[f,H,G,z,$])}const Vd=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?yr({element:r.current,padding:o}).fn(n):{}:r?yr({element:r,padding:o}).fn(n):{}}}},Gd=(e,t)=>({...Od(e),options:[e,t]}),Kd=(e,t)=>({...kd(e),options:[e,t]}),Yd=(e,t)=>({...Bd(e),options:[e,t]}),qd=(e,t)=>({...Ld(e),options:[e,t]}),Xd=(e,t)=>({...Fd(e),options:[e,t]}),Zd=(e,t)=>({...$d(e),options:[e,t]}),Jd=(e,t)=>({...Vd(e),options:[e,t]});var Qd="Arrow",ko=i.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...a}=e;return s.jsx(Y.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:s.jsx("polygon",{points:"0,0 30,0 15,10"})})});ko.displayName=Qd;var eu=ko;function tu(e){const[t,n]=i.useState(void 0);return Se(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const a=o[0];let c,l;if("borderBoxSize"in a){const d=a.borderBoxSize,u=Array.isArray(d)?d[0]:d;c=u.inlineSize,l=u.blockSize}else c=e.offsetWidth,l=e.offsetHeight;n({width:c,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Fn="Popper",[Lo,It]=je(Fn),[nu,Fo]=Lo(Fn),$o=e=>{const{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return s.jsx(nu,{scope:t,anchor:r,onAnchorChange:o,children:n})};$o.displayName=Fn;var Bo="PopperAnchor",zo=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,a=Fo(Bo,n),c=i.useRef(null),l=q(t,c);return i.useEffect(()=>{a.onAnchorChange(r?.current||c.current)}),r?null:s.jsx(Y.div,{...o,ref:l})});zo.displayName=Bo;var $n="PopperContent",[ru,ou]=Lo($n),Uo=i.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:a="center",alignOffset:c=0,arrowPadding:l=0,avoidCollisions:d=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:h,...m}=e,x=Fo($n,n),[b,w]=i.useState(null),y=q(t,F=>w(F)),[C,S]=i.useState(null),R=tu(C),N=R?.width??0,D=R?.height??0,E=r+(a!=="center"?"-"+a:""),T=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},O=Array.isArray(u)?u:[u],W=O.length>0,H={padding:T,boundary:O.filter(au),altBoundary:W},{refs:U,floatingStyles:G,placement:z,isPositioned:$,middlewareData:k}=Wd({strategy:"fixed",placement:E,whileElementsMounted:(...F)=>Id(...F,{animationFrame:v==="always"}),elements:{reference:x.anchor},middleware:[Gd({mainAxis:o+D,alignmentAxis:c}),d&&Kd({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?Yd():void 0,...H}),d&&qd({...H}),Xd({...H,apply:({elements:F,rects:ne,availableWidth:He,availableHeight:rt})=>{const{width:$a,height:Ba}=ne.reference,ot=F.floating.style;ot.setProperty("--radix-popper-available-width",`${He}px`),ot.setProperty("--radix-popper-available-height",`${rt}px`),ot.setProperty("--radix-popper-anchor-width",`${$a}px`),ot.setProperty("--radix-popper-anchor-height",`${Ba}px`)}}),C&&Jd({element:C,padding:l}),iu({arrowWidth:N,arrowHeight:D}),g&&Zd({strategy:"referenceHidden",...H})]}),[A,P]=Vo(z),B=ve(h);Se(()=>{$&&B?.()},[$,B]);const J=k.arrow?.x,j=k.arrow?.y,I=k.arrow?.centerOffset!==0,[_,V]=i.useState();return Se(()=>{b&&V(window.getComputedStyle(b).zIndex)},[b]),s.jsx("div",{ref:U.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:$?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:_,"--radix-popper-transform-origin":[k.transformOrigin?.x,k.transformOrigin?.y].join(" "),...k.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:s.jsx(ru,{scope:n,placedSide:A,onArrowChange:S,arrowX:J,arrowY:j,shouldHideArrow:I,children:s.jsx(Y.div,{"data-side":A,"data-align":P,...m,ref:y,style:{...m.style,animation:$?void 0:"none"}})})})});Uo.displayName=$n;var Ho="PopperArrow",su={top:"bottom",right:"left",bottom:"top",left:"right"},Wo=i.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,a=ou(Ho,r),c=su[a.placedSide];return s.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:s.jsx(eu,{...o,ref:n,style:{...o.style,display:"block"}})})});Wo.displayName=Ho;function au(e){return e!==null}var iu=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,c=o.arrow?.centerOffset!==0,l=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[u,f]=Vo(n),p={start:"0%",center:"50%",end:"100%"}[f],g=(o.arrow?.x??0)+l/2,v=(o.arrow?.y??0)+d/2;let h="",m="";return u==="bottom"?(h=c?p:`${g}px`,m=`${-d}px`):u==="top"?(h=c?p:`${g}px`,m=`${r.floating.height+d}px`):u==="right"?(h=`${-d}px`,m=c?p:`${v}px`):u==="left"&&(h=`${r.floating.width+d}px`,m=c?p:`${v}px`),{data:{x:h,y:m}}}});function Vo(e){const[t,n="center"]=e.split("-");return[t,n]}var Go=$o,Ko=zo,Yo=Uo,qo=Wo,cu=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),lu="VisuallyHidden",Xo=i.forwardRef((e,t)=>s.jsx(Y.span,{...e,ref:t,style:{...cu,...e.style}}));Xo.displayName=lu;var du=Xo,[Ot,Rm]=je("Tooltip",[It]),kt=It(),Zo="TooltipProvider",uu=700,dn="tooltip.open",[fu,Bn]=Ot(Zo),Jo=e=>{const{__scopeTooltip:t,delayDuration:n=uu,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:a}=e,c=i.useRef(!0),l=i.useRef(!1),d=i.useRef(0);return i.useEffect(()=>{const u=d.current;return()=>window.clearTimeout(u)},[]),s.jsx(fu,{scope:t,isOpenDelayedRef:c,delayDuration:n,onOpen:i.useCallback(()=>{window.clearTimeout(d.current),c.current=!1},[]),onClose:i.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>c.current=!0,r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:i.useCallback(u=>{l.current=u},[]),disableHoverableContent:o,children:a})};Jo.displayName=Zo;var Ye="Tooltip",[pu,Lt]=Ot(Ye),Qo=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:a,disableHoverableContent:c,delayDuration:l}=e,d=Bn(Ye,e.__scopeTooltip),u=kt(t),[f,p]=i.useState(null),g=Ae(),v=i.useRef(0),h=c??d.disableHoverableContent,m=l??d.delayDuration,x=i.useRef(!1),[b,w]=jt({prop:r,defaultProp:o??!1,onChange:N=>{N?(d.onOpen(),document.dispatchEvent(new CustomEvent(dn))):d.onClose(),a?.(N)},caller:Ye}),y=i.useMemo(()=>b?x.current?"delayed-open":"instant-open":"closed",[b]),C=i.useCallback(()=>{window.clearTimeout(v.current),v.current=0,x.current=!1,w(!0)},[w]),S=i.useCallback(()=>{window.clearTimeout(v.current),v.current=0,w(!1)},[w]),R=i.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{x.current=!0,w(!0),v.current=0},m)},[m,w]);return i.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),s.jsx(Go,{...u,children:s.jsx(pu,{scope:t,contentId:g,open:b,stateAttribute:y,trigger:f,onTriggerChange:p,onTriggerEnter:i.useCallback(()=>{d.isOpenDelayedRef.current?R():C()},[d.isOpenDelayedRef,R,C]),onTriggerLeave:i.useCallback(()=>{h?S():(window.clearTimeout(v.current),v.current=0)},[S,h]),onOpen:C,onClose:S,disableHoverableContent:h,children:n})})};Qo.displayName=Ye;var un="TooltipTrigger",es=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Lt(un,n),a=Bn(un,n),c=kt(n),l=i.useRef(null),d=q(t,l,o.onTriggerChange),u=i.useRef(!1),f=i.useRef(!1),p=i.useCallback(()=>u.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),s.jsx(Ko,{asChild:!0,...c,children:s.jsx(Y.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:d,onPointerMove:L(e.onPointerMove,g=>{g.pointerType!=="touch"&&!f.current&&!a.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:L(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:L(e.onPointerDown,()=>{o.open&&o.onClose(),u.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:L(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:L(e.onBlur,o.onClose),onClick:L(e.onClick,o.onClose)})})});es.displayName=un;var mu="TooltipPortal",[jm,hu]=Ot(mu,{forceMount:void 0}),Be="TooltipContent",ts=i.forwardRef((e,t)=>{const n=hu(Be,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...a}=e,c=Lt(Be,e.__scopeTooltip);return s.jsx(we,{present:r||c.open,children:c.disableHoverableContent?s.jsx(ns,{side:o,...a,ref:t}):s.jsx(gu,{side:o,...a,ref:t})})}),gu=i.forwardRef((e,t)=>{const n=Lt(Be,e.__scopeTooltip),r=Bn(Be,e.__scopeTooltip),o=i.useRef(null),a=q(t,o),[c,l]=i.useState(null),{trigger:d,onClose:u}=n,f=o.current,{onPointerInTransitChange:p}=r,g=i.useCallback(()=>{l(null),p(!1)},[p]),v=i.useCallback((h,m)=>{const x=h.currentTarget,b={x:h.clientX,y:h.clientY},w=yu(b,x.getBoundingClientRect()),y=Cu(b,w),C=Nu(m.getBoundingClientRect()),S=Eu([...y,...C]);l(S),p(!0)},[p]);return i.useEffect(()=>()=>g(),[g]),i.useEffect(()=>{if(d&&f){const h=x=>v(x,f),m=x=>v(x,d);return d.addEventListener("pointerleave",h),f.addEventListener("pointerleave",m),()=>{d.removeEventListener("pointerleave",h),f.removeEventListener("pointerleave",m)}}},[d,f,v,g]),i.useEffect(()=>{if(c){const h=m=>{const x=m.target,b={x:m.clientX,y:m.clientY},w=d?.contains(x)||f?.contains(x),y=!Su(b,c);w?g():y&&(g(),u())};return document.addEventListener("pointermove",h),()=>document.removeEventListener("pointermove",h)}},[d,f,c,u,g]),s.jsx(ns,{...e,ref:a})}),[vu,xu]=Ot(Ye,{isInside:!1}),bu=kr("TooltipContent"),ns=i.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:c,...l}=e,d=Lt(Be,n),u=kt(n),{onClose:f}=d;return i.useEffect(()=>(document.addEventListener(dn,f),()=>document.removeEventListener(dn,f)),[f]),i.useEffect(()=>{if(d.trigger){const p=g=>{g.target?.contains(d.trigger)&&f()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[d.trigger,f]),s.jsx(At,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:p=>p.preventDefault(),onDismiss:f,children:s.jsxs(Yo,{"data-state":d.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[s.jsx(bu,{children:r}),s.jsx(vu,{scope:n,isInside:!0,children:s.jsx(du,{id:d.contentId,role:"tooltip",children:o||r})})]})})});ts.displayName=Be;var rs="TooltipArrow",wu=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=kt(n);return xu(rs,n).isInside?null:s.jsx(qo,{...o,...r,ref:t})});wu.displayName=rs;function yu(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,r,o,a)){case a:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Cu(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Nu(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Su(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,c=t.length-1;a<t.length;c=a++){const l=t[a],d=t[c],u=l.x,f=l.y,p=d.x,g=d.y;f>r!=g>r&&n<(p-u)*(r-f)/(g-f)+u&&(o=!o)}return o}function Eu(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Ru(t)}function Ru(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const a=t[t.length-1],c=t[t.length-2];if((a.x-c.x)*(o.y-c.y)>=(a.y-c.y)*(o.x-c.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const a=n[n.length-1],c=n[n.length-2];if((a.x-c.x)*(o.y-c.y)>=(a.y-c.y)*(o.x-c.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var ju=Jo,Au=Qo,Mu=es,os=ts;const Du=ju,Pu=Au,_u=Mu,ss=i.forwardRef(({className:e,sideOffset:t=4,...n},r)=>s.jsx(os,{ref:r,sideOffset:t,className:M("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));ss.displayName=os.displayName;const Tu="sidebar_state",Iu=3600*24*7,Ou="20rem",ku="22rem",Lu="3rem",Fu="b",as=i.createContext(null);function Ft(){const e=i.useContext(as);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}const is=i.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:a,...c},l)=>{const d=vc(),[u,f]=i.useState(!1),[p,g]=i.useState(e),v=t??p,h=i.useCallback(w=>{const y=typeof w=="function"?w(v):w;n?n(y):g(y),document.cookie=`${Tu}=${y}; path=/; max-age=${Iu}`},[n,v]),m=i.useCallback(()=>d?f(w=>!w):h(w=>!w),[d,h,f]);i.useEffect(()=>{const w=y=>{y.key===Fu&&(y.metaKey||y.ctrlKey)&&(y.preventDefault(),m())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[m]);const x=v?"expanded":"collapsed",b=i.useMemo(()=>({state:x,open:v,setOpen:h,isMobile:d,openMobile:u,setOpenMobile:f,toggleSidebar:m}),[x,v,h,d,u,f,m]);return s.jsx(as.Provider,{value:b,children:s.jsx(Du,{delayDuration:0,children:s.jsx("div",{style:{"--sidebar-width":Ou,"--sidebar-width-icon":Lu,...o},className:M("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",r),ref:l,...c,children:a})})})});is.displayName="SidebarProvider";const $u=i.forwardRef(({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...a},c)=>{const{isMobile:l,state:d,openMobile:u,setOpenMobile:f}=Ft();return n==="none"?s.jsx("div",{className:M("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:c,...a,children:o}):l?s.jsx(Fl,{open:u,onOpenChange:f,...a,children:s.jsx(So,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":ku},side:e,children:s.jsx("div",{className:"flex h-full w-full flex-col",children:o})})}):s.jsxs("div",{ref:c,className:"group peer hidden md:block text-sidebar-foreground","data-state":d,"data-collapsible":d==="collapsed"?n:"","data-variant":t,"data-side":e,children:[s.jsx("div",{className:M("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),s.jsx("div",{className:M("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...a,children:s.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:o})})]})});$u.displayName="Sidebar";const Bu=i.forwardRef(({className:e,onClick:t,...n},r)=>{const{toggleSidebar:o}=Ft();return s.jsxs(Q,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:M("h-7 w-7",e),onClick:a=>{t?.(a),o()},...n,children:[s.jsx(_i,{}),s.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});Bu.displayName="SidebarTrigger";const zu=i.forwardRef(({className:e,...t},n)=>{const{toggleSidebar:r}=Ft();return s.jsx("button",{ref:n,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:M("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});zu.displayName="SidebarRail";const Uu=i.forwardRef(({className:e,...t},n)=>s.jsx("main",{ref:n,className:M("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));Uu.displayName="SidebarInset";const Hu=i.forwardRef(({className:e,...t},n)=>s.jsx(Rt,{ref:n,"data-sidebar":"input",className:M("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t}));Hu.displayName="SidebarInput";const Wu=i.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"header",className:M("flex flex-col gap-2 p-2",e),...t}));Wu.displayName="SidebarHeader";const Vu=i.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"footer",className:M("flex flex-col gap-2 p-2",e),...t}));Vu.displayName="SidebarFooter";const Gu=i.forwardRef(({className:e,...t},n)=>s.jsx(ei,{ref:n,"data-sidebar":"separator",className:M("mx-2 w-auto bg-sidebar-border",e),...t}));Gu.displayName="SidebarSeparator";const Ku=i.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"content",className:M("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));Ku.displayName="SidebarContent";const Yu=i.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"group",className:M("relative flex w-full min-w-0 flex-col p-2",e),...t}));Yu.displayName="SidebarGroup";const qu=i.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Ze:"div";return s.jsx(o,{ref:r,"data-sidebar":"group-label",className:M("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})});qu.displayName="SidebarGroupLabel";const Xu=i.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Ze:"button";return s.jsx(o,{ref:r,"data-sidebar":"group-action",className:M("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...n})});Xu.displayName="SidebarGroupAction";const Zu=i.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"group-content",className:M("w-full text-sm",e),...t}));Zu.displayName="SidebarGroupContent";const Ju=i.forwardRef(({className:e,...t},n)=>s.jsx("ul",{ref:n,"data-sidebar":"menu",className:M("flex w-full min-w-0 flex-col gap-1",e),...t}));Ju.displayName="SidebarMenu";const Qu=i.forwardRef(({className:e,...t},n)=>s.jsx("li",{ref:n,"data-sidebar":"menu-item",className:M("group/menu-item relative",e),...t}));Qu.displayName="SidebarMenuItem";const ef=vn("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!size-8"}},defaultVariants:{variant:"default",size:"default"}}),tf=i.forwardRef(({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:a,...c},l)=>{const d=e?Ze:"button",{isMobile:u,state:f}=Ft(),p=s.jsx(d,{ref:l,"data-sidebar":"menu-button","data-size":r,"data-active":t,className:M(ef({variant:n,size:r}),a),...c});return o?(typeof o=="string"&&(o={children:o}),s.jsxs(Pu,{children:[s.jsx(_u,{asChild:!0,children:p}),s.jsx(ss,{side:"right",align:"center",hidden:f!=="collapsed"||u,...o})]})):p});tf.displayName="SidebarMenuButton";const nf=i.forwardRef(({className:e,asChild:t=!1,showOnHover:n=!1,...r},o)=>{const a=t?Ze:"button";return s.jsx(a,{ref:o,"data-sidebar":"menu-action",className:M("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});nf.displayName="SidebarMenuAction";const rf=i.forwardRef(({className:e,...t},n)=>s.jsx("div",{ref:n,"data-sidebar":"menu-badge",className:M("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t}));rf.displayName="SidebarMenuBadge";const of=i.forwardRef(({className:e,showIcon:t=!1,...n},r)=>{const o=i.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return s.jsxs("div",{ref:r,"data-sidebar":"menu-skeleton",className:M("rounded-md h-8 flex gap-2 px-2 items-center",e),...n,children:[t&&s.jsx(fr,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),s.jsx(fr,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})});of.displayName="SidebarMenuSkeleton";const sf=i.forwardRef(({className:e,...t},n)=>s.jsx("ul",{ref:n,"data-sidebar":"menu-sub",className:M("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));sf.displayName="SidebarMenuSub";const af=i.forwardRef(({...e},t)=>s.jsx("li",{ref:t,...e}));af.displayName="SidebarMenuSubItem";const cf=i.forwardRef(({asChild:e=!1,size:t="md",isActive:n,className:r,...o},a)=>{const c=e?Ze:"a";return s.jsx(c,{ref:a,"data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:M("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...o})});cf.displayName="SidebarMenuSubButton";var cs="AlertDialog",[lf,Am]=je(cs,[oo]),ye=oo(),ls=e=>{const{__scopeAlertDialog:t,...n}=e,r=ye(t);return s.jsx(yo,{...r,...n,modal:!0})};ls.displayName=cs;var df="AlertDialogTrigger",ds=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ye(n);return s.jsx(Ll,{...o,...r,ref:t})});ds.displayName=df;var uf="AlertDialogPortal",us=e=>{const{__scopeAlertDialog:t,...n}=e,r=ye(t);return s.jsx(Co,{...r,...n})};us.displayName=uf;var ff="AlertDialogOverlay",fs=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ye(n);return s.jsx(Rn,{...o,...r,ref:t})});fs.displayName=ff;var Fe="AlertDialogContent",[pf,mf]=lf(Fe),hf=kr("AlertDialogContent"),ps=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:r,...o}=e,a=ye(n),c=i.useRef(null),l=q(t,c),d=i.useRef(null);return s.jsx(Tl,{contentName:Fe,titleName:ms,docsSlug:"alert-dialog",children:s.jsx(pf,{scope:n,cancelRef:d,children:s.jsxs(jn,{role:"alertdialog",...a,...o,ref:l,onOpenAutoFocus:L(o.onOpenAutoFocus,u=>{u.preventDefault(),d.current?.focus({preventScroll:!0})}),onPointerDownOutside:u=>u.preventDefault(),onInteractOutside:u=>u.preventDefault(),children:[s.jsx(hf,{children:r}),s.jsx(vf,{contentRef:c})]})})})});ps.displayName=Fe;var ms="AlertDialogTitle",hs=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ye(n);return s.jsx(An,{...o,...r,ref:t})});hs.displayName=ms;var gs="AlertDialogDescription",vs=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ye(n);return s.jsx(Mn,{...o,...r,ref:t})});vs.displayName=gs;var gf="AlertDialogAction",xs=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ye(n);return s.jsx(Dn,{...o,...r,ref:t})});xs.displayName=gf;var bs="AlertDialogCancel",ws=i.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=mf(bs,n),a=ye(n),c=q(t,o);return s.jsx(Dn,{...a,...r,ref:c})});ws.displayName=bs;var vf=({contentRef:e})=>{const t=`\`${Fe}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${Fe}\` by passing a \`${gs}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${Fe}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return i.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},xf=ls,bf=ds,wf=us,ys=fs,Cs=ps,Ns=xs,Ss=ws,Es=hs,Rs=vs;const yf=xf,Cf=bf,Nf=wf,js=i.forwardRef(({className:e,...t},n)=>s.jsx(ys,{className:M("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));js.displayName=ys.displayName;const As=i.forwardRef(({className:e,...t},n)=>s.jsxs(Nf,{children:[s.jsx(js,{}),s.jsx(Cs,{ref:n,className:M("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));As.displayName=Cs.displayName;const Ms=({className:e,...t})=>s.jsx("div",{className:M("flex flex-col space-y-2 text-center sm:text-left",e),...t});Ms.displayName="AlertDialogHeader";const Ds=({className:e,...t})=>s.jsx("div",{className:M("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});Ds.displayName="AlertDialogFooter";const Ps=i.forwardRef(({className:e,...t},n)=>s.jsx(Es,{ref:n,className:M("text-lg font-semibold",e),...t}));Ps.displayName=Es.displayName;const _s=i.forwardRef(({className:e,...t},n)=>s.jsx(Rs,{ref:n,className:M("text-sm text-muted-foreground",e),...t}));_s.displayName=Rs.displayName;const Ts=i.forwardRef(({className:e,...t},n)=>s.jsx(Ns,{ref:n,className:M(Lr(),e),...t}));Ts.displayName=Ns.displayName;const Is=i.forwardRef(({className:e,...t},n)=>s.jsx(Ss,{ref:n,className:M(Lr({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));Is.displayName=Ss.displayName;function Sf({children:e,isCollapsed:t,onToggle:n,currentMode:r="无忧问答"}){const{conversations:o,currentConversationId:a,createConversation:c,deleteConversation:l,updateConversationTitle:d,setCurrentConversation:u,loadUserConversations:f,getConversationsByMode:p,error:g}=rn(),v=p(r),{user:h}=ue(),[m,x]=i.useState(null),[b,w]=i.useState(""),[y,C]=i.useState(!1),[S,R]=i.useState(!1),[N,D]=i.useState(!1),[E,T]=i.useState(new Set),O=t!==void 0?t:y,W=()=>{u(null),window.location.reload()},H=async P=>{T(B=>new Set(B).add(P));try{await l(P),T(B=>{const J=new Set(B);return J.delete(P),J})}catch(B){console.error("删除对话失败:",B),T(J=>{const j=new Set(J);return j.delete(P),j})}},U=(P,B,J)=>{J.stopPropagation(),x(P),w(B)},G=async P=>{if(b.trim())try{await d(P,b.trim())}catch(B){console.error("更新标题失败:",B)}x(null),w("")},z=()=>{x(null),w("")},$=()=>{O?(n?n():C(!1),setTimeout(()=>{R(!1)},150)):(R(!0),setTimeout(()=>{n?n():C(!0)},150))};i.useEffect(()=>{O?R(!0):setTimeout(()=>{R(!1)},300)},[O]),i.useEffect(()=>{const P=()=>{D(window.innerWidth<768)};return P(),window.addEventListener("resize",P),()=>window.removeEventListener("resize",P)},[]),i.useEffect(()=>{N&&!y&&t===void 0&&C(!0)},[N]),i.useEffect(()=>{h&&f()},[h,f]),i.useEffect(()=>{const P=B=>{B.key==="b"&&(B.ctrlKey||B.metaKey)&&(B.preventDefault(),$())};return window.addEventListener("keydown",P),()=>window.removeEventListener("keydown",P)},[O]);const k=()=>{N&&!O&&$()},A=P=>{const B=P instanceof Date?P:new Date(P);if(isNaN(B.getTime()))return"无效日期";const j=new Date().getTime()-B.getTime(),I=Math.floor(j/(1e3*60*60*24));return I===0?"今天":I===1?"昨天":I<7?`${I}天前`:B.toLocaleDateString("zh-CN")};return s.jsxs(is,{defaultOpen:!0,children:[N&&!O&&s.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:k}),N&&s.jsx(Q,{variant:"outline",size:"sm",className:"fixed top-4 left-4 z-50 md:hidden h-10 w-10 p-0",onClick:$,"aria-label":O?"展开侧边栏":"收起侧边栏",children:s.jsx(Si,{className:"h-4 w-4"})}),s.jsxs("div",{className:M("flex min-h-screen w-full",N?"relative":""),children:[s.jsx("div",{className:M("bg-sidebar border-r border-border transition-all duration-300 ease-in-out z-50",!N&&"flex-shrink-0",!N&&(O?"w-0 overflow-hidden":"w-80"),N&&"fixed top-0 left-0 h-full",N&&(O?"-translate-x-full":"translate-x-0 w-80")),children:s.jsxs("div",{className:"h-full flex flex-col",children:[s.jsxs("div",{className:M("border-b px-4 h-16 py-3 transition-opacity duration-150 ease-in-out",S?"opacity-0":"opacity-100"),children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-lg font-semibold",children:"对话历史"}),s.jsx(Q,{onClick:W,size:"sm",variant:"outline",className:"h-8 w-8 p-0",disabled:!h,children:s.jsx(ki,{className:"h-4 w-4"})})]}),g&&s.jsx("div",{className:"mt-2 text-xs text-red-500 bg-red-50 p-2 rounded",children:g})]}),s.jsx("div",{className:M("flex-1 overflow-auto transition-opacity duration-150 ease-in-out",S?"opacity-0":"opacity-100"),children:s.jsx("div",{className:"p-2",children:h?v.length===0?s.jsxs("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:["暂无 ",r," 对话历史"]}):s.jsx("div",{className:"space-y-1",children:v.filter(P=>!E.has(P.id)).map(P=>s.jsxs("div",{className:M("group relative flex items-center gap-3 rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-accent transition-colors",a===P.id&&"bg-accent"),onClick:()=>u(P.id),children:[s.jsx(Ri,{className:"h-4 w-4 flex-shrink-0"}),s.jsx("div",{className:"flex-1 min-w-0",children:m===P.id?s.jsxs("div",{className:"flex items-center gap-1",onClick:B=>B.stopPropagation(),children:[s.jsx(Rt,{value:b,onChange:B=>w(B.target.value),className:"h-6 text-xs",onKeyDown:B=>{B.key==="Enter"?G(P.id):B.key==="Escape"&&z()},autoFocus:!0}),s.jsx(Q,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:()=>G(P.id),children:s.jsx($r,{className:"h-3 w-3"})}),s.jsx(Q,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:z,children:s.jsx(Et,{className:"h-3 w-3"})})]}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"font-medium truncate",children:P.title}),s.jsxs("div",{className:"text-xs text-muted-foreground",children:[A(P.updatedAt)," •"," ",P.messages.length," 条消息"]})]})}),m!==P.id&&s.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[s.jsx(Q,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:B=>U(P.id,P.title,B),children:s.jsx(Ii,{className:"h-3 w-3"})}),s.jsxs(yf,{children:[s.jsx(Cf,{asChild:!0,children:s.jsx(Q,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0 text-destructive hover:text-destructive",onClick:B=>B.stopPropagation(),children:s.jsx(Fi,{className:"h-3 w-3"})})}),s.jsxs(As,{children:[s.jsxs(Ms,{children:[s.jsx(Ps,{children:"确认删除对话"}),s.jsxs(_s,{children:['确定要删除对话 "',P.title,'" 吗？此操作不可撤销，对话中的所有消息都将被永久删除。']})]}),s.jsxs(Ds,{children:[s.jsx(Is,{children:"取消"}),s.jsx(Ts,{onClick:()=>H(P.id),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"删除"})]})]})]})]})]},P.id))}):s.jsx("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:"请先登录"})})}),s.jsx("div",{className:M("border-t px-4 py-3 transition-opacity duration-150 ease-in-out",S?"opacity-0":"opacity-100"),children:s.jsxs("div",{className:"text-xs text-muted-foreground",children:["共 ",v.length," 个 ",r," 对话"]})})]})}),s.jsx("main",{className:M("flex-1 flex flex-col min-w-0 w-full relative",N&&"w-full"),children:e})]})]})}function Os(e){const t=e+"CollectionProvider",[n,r]=je(t),[o,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=m=>{const{scope:x,children:b}=m,w=de.useRef(null),y=de.useRef(new Map).current;return s.jsx(o,{scope:x,itemMap:y,collectionRef:w,children:b})};c.displayName=t;const l=e+"CollectionSlot",d=gt(l),u=de.forwardRef((m,x)=>{const{scope:b,children:w}=m,y=a(l,b),C=q(x,y.collectionRef);return s.jsx(d,{ref:C,children:w})});u.displayName=l;const f=e+"CollectionItemSlot",p="data-radix-collection-item",g=gt(f),v=de.forwardRef((m,x)=>{const{scope:b,children:w,...y}=m,C=de.useRef(null),S=q(x,C),R=a(f,b);return de.useEffect(()=>(R.itemMap.set(C,{ref:C,...y}),()=>void R.itemMap.delete(C))),s.jsx(g,{[p]:"",ref:S,children:w})});v.displayName=f;function h(m){const x=a(e+"CollectionConsumer",m);return de.useCallback(()=>{const w=x.collectionRef.current;if(!w)return[];const y=Array.from(w.querySelectorAll(`[${p}]`));return Array.from(x.itemMap.values()).sort((R,N)=>y.indexOf(R.ref.current)-y.indexOf(N.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:c,Slot:u,ItemSlot:v},h,r]}var Ef=i.createContext(void 0);function ks(e){const t=i.useContext(Ef);return e||t||"ltr"}var en="rovingFocusGroup.onEntryFocus",Rf={bubbles:!1,cancelable:!0},et="RovingFocusGroup",[fn,Ls,jf]=Os(et),[Af,Fs]=je(et,[jf]),[Mf,Df]=Af(et),$s=i.forwardRef((e,t)=>s.jsx(fn.Provider,{scope:e.__scopeRovingFocusGroup,children:s.jsx(fn.Slot,{scope:e.__scopeRovingFocusGroup,children:s.jsx(Pf,{...e,ref:t})})}));$s.displayName=et;var Pf=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:c,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:d,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...p}=e,g=i.useRef(null),v=q(t,g),h=ks(a),[m,x]=jt({prop:c,defaultProp:l??null,onChange:d,caller:et}),[b,w]=i.useState(!1),y=ve(u),C=Ls(n),S=i.useRef(!1),[R,N]=i.useState(0);return i.useEffect(()=>{const D=g.current;if(D)return D.addEventListener(en,y),()=>D.removeEventListener(en,y)},[y]),s.jsx(Mf,{scope:n,orientation:r,dir:h,loop:o,currentTabStopId:m,onItemFocus:i.useCallback(D=>x(D),[x]),onItemShiftTab:i.useCallback(()=>w(!0),[]),onFocusableItemAdd:i.useCallback(()=>N(D=>D+1),[]),onFocusableItemRemove:i.useCallback(()=>N(D=>D-1),[]),children:s.jsx(Y.div,{tabIndex:b||R===0?-1:0,"data-orientation":r,...p,ref:v,style:{outline:"none",...e.style},onMouseDown:L(e.onMouseDown,()=>{S.current=!0}),onFocus:L(e.onFocus,D=>{const E=!S.current;if(D.target===D.currentTarget&&E&&!b){const T=new CustomEvent(en,Rf);if(D.currentTarget.dispatchEvent(T),!T.defaultPrevented){const O=C().filter(z=>z.focusable),W=O.find(z=>z.active),H=O.find(z=>z.id===m),G=[W,H,...O].filter(Boolean).map(z=>z.ref.current);Us(G,f)}}S.current=!1}),onBlur:L(e.onBlur,()=>w(!1))})})}),Bs="RovingFocusGroupItem",zs=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,children:c,...l}=e,d=Ae(),u=a||d,f=Df(Bs,n),p=f.currentTabStopId===u,g=Ls(n),{onFocusableItemAdd:v,onFocusableItemRemove:h,currentTabStopId:m}=f;return i.useEffect(()=>{if(r)return v(),()=>h()},[r,v,h]),s.jsx(fn.ItemSlot,{scope:n,id:u,focusable:r,active:o,children:s.jsx(Y.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...l,ref:t,onMouseDown:L(e.onMouseDown,x=>{r?f.onItemFocus(u):x.preventDefault()}),onFocus:L(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:L(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){f.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const b=If(x,f.orientation,f.dir);if(b!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let y=g().filter(C=>C.focusable).map(C=>C.ref.current);if(b==="last")y.reverse();else if(b==="prev"||b==="next"){b==="prev"&&y.reverse();const C=y.indexOf(x.currentTarget);y=f.loop?Of(y,C+1):y.slice(C+1)}setTimeout(()=>Us(y))}}),children:typeof c=="function"?c({isCurrentTabStop:p,hasTabStop:m!=null}):c})})});zs.displayName=Bs;var _f={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Tf(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function If(e,t,n){const r=Tf(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return _f[r]}function Us(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Of(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var kf=$s,Lf=zs,pn=["Enter"," "],Ff=["ArrowDown","PageUp","Home"],Hs=["ArrowUp","PageDown","End"],$f=[...Ff,...Hs],Bf={ltr:[...pn,"ArrowRight"],rtl:[...pn,"ArrowLeft"]},zf={ltr:["ArrowLeft"],rtl:["ArrowRight"]},tt="Menu",[qe,Uf,Hf]=Os(tt),[Pe,Ws]=je(tt,[Hf,It,Fs]),$t=It(),Vs=Fs(),[Wf,_e]=Pe(tt),[Vf,nt]=Pe(tt),Gs=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:a,modal:c=!0}=e,l=$t(t),[d,u]=i.useState(null),f=i.useRef(!1),p=ve(a),g=ks(o);return i.useEffect(()=>{const v=()=>{f.current=!0,document.addEventListener("pointerdown",h,{capture:!0,once:!0}),document.addEventListener("pointermove",h,{capture:!0,once:!0})},h=()=>f.current=!1;return document.addEventListener("keydown",v,{capture:!0}),()=>{document.removeEventListener("keydown",v,{capture:!0}),document.removeEventListener("pointerdown",h,{capture:!0}),document.removeEventListener("pointermove",h,{capture:!0})}},[]),s.jsx(Go,{...l,children:s.jsx(Wf,{scope:t,open:n,onOpenChange:p,content:d,onContentChange:u,children:s.jsx(Vf,{scope:t,onClose:i.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:g,modal:c,children:r})})})};Gs.displayName=tt;var Gf="MenuAnchor",zn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=$t(n);return s.jsx(Ko,{...o,...r,ref:t})});zn.displayName=Gf;var Un="MenuPortal",[Kf,Ks]=Pe(Un,{forceMount:void 0}),Ys=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=_e(Un,t);return s.jsx(Kf,{scope:t,forceMount:n,children:s.jsx(we,{present:n||a.open,children:s.jsx(yn,{asChild:!0,container:o,children:r})})})};Ys.displayName=Un;var ae="MenuContent",[Yf,Hn]=Pe(ae),qs=i.forwardRef((e,t)=>{const n=Ks(ae,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=_e(ae,e.__scopeMenu),c=nt(ae,e.__scopeMenu);return s.jsx(qe.Provider,{scope:e.__scopeMenu,children:s.jsx(we,{present:r||a.open,children:s.jsx(qe.Slot,{scope:e.__scopeMenu,children:c.modal?s.jsx(qf,{...o,ref:t}):s.jsx(Xf,{...o,ref:t})})})})}),qf=i.forwardRef((e,t)=>{const n=_e(ae,e.__scopeMenu),r=i.useRef(null),o=q(t,r);return i.useEffect(()=>{const a=r.current;if(a)return no(a)},[]),s.jsx(Wn,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:L(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Xf=i.forwardRef((e,t)=>{const n=_e(ae,e.__scopeMenu);return s.jsx(Wn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Zf=gt("MenuContent.ScrollLock"),Wn=i.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:c,disableOutsidePointerEvents:l,onEntryFocus:d,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:g,onDismiss:v,disableOutsideScroll:h,...m}=e,x=_e(ae,n),b=nt(ae,n),w=$t(n),y=Vs(n),C=Uf(n),[S,R]=i.useState(null),N=i.useRef(null),D=q(t,N,x.onContentChange),E=i.useRef(0),T=i.useRef(""),O=i.useRef(0),W=i.useRef(null),H=i.useRef("right"),U=i.useRef(0),G=h?Cn:i.Fragment,z=h?{as:Zf,allowPinchZoom:!0}:void 0,$=A=>{const P=T.current+A,B=C().filter(F=>!F.disabled),J=document.activeElement,j=B.find(F=>F.ref.current===J)?.textValue,I=B.map(F=>F.textValue),_=lp(I,P,j),V=B.find(F=>F.textValue===_)?.ref.current;(function F(ne){T.current=ne,window.clearTimeout(E.current),ne!==""&&(E.current=window.setTimeout(()=>F(""),1e3))})(P),V&&setTimeout(()=>V.focus())};i.useEffect(()=>()=>window.clearTimeout(E.current),[]),Kr();const k=i.useCallback(A=>H.current===W.current?.side&&up(A,W.current?.area),[]);return s.jsx(Yf,{scope:n,searchRef:T,onItemEnter:i.useCallback(A=>{k(A)&&A.preventDefault()},[k]),onItemLeave:i.useCallback(A=>{k(A)||(N.current?.focus(),R(null))},[k]),onTriggerLeave:i.useCallback(A=>{k(A)&&A.preventDefault()},[k]),pointerGraceTimerRef:O,onPointerGraceIntentChange:i.useCallback(A=>{W.current=A},[]),children:s.jsx(G,{...z,children:s.jsx(wn,{asChild:!0,trapped:o,onMountAutoFocus:L(a,A=>{A.preventDefault(),N.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:s.jsx(At,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:g,onDismiss:v,children:s.jsx(kf,{asChild:!0,...y,dir:b.dir,orientation:"vertical",loop:r,currentTabStopId:S,onCurrentTabStopIdChange:R,onEntryFocus:L(d,A=>{b.isUsingKeyboardRef.current||A.preventDefault()}),preventScrollOnEntryFocus:!0,children:s.jsx(Yo,{role:"menu","aria-orientation":"vertical","data-state":ua(x.open),"data-radix-menu-content":"",dir:b.dir,...w,...m,ref:D,style:{outline:"none",...m.style},onKeyDown:L(m.onKeyDown,A=>{const B=A.target.closest("[data-radix-menu-content]")===A.currentTarget,J=A.ctrlKey||A.altKey||A.metaKey,j=A.key.length===1;B&&(A.key==="Tab"&&A.preventDefault(),!J&&j&&$(A.key));const I=N.current;if(A.target!==I||!$f.includes(A.key))return;A.preventDefault();const V=C().filter(F=>!F.disabled).map(F=>F.ref.current);Hs.includes(A.key)&&V.reverse(),ip(V)}),onBlur:L(e.onBlur,A=>{A.currentTarget.contains(A.target)||(window.clearTimeout(E.current),T.current="")}),onPointerMove:L(e.onPointerMove,Xe(A=>{const P=A.target,B=U.current!==A.clientX;if(A.currentTarget.contains(P)&&B){const J=A.clientX>U.current?"right":"left";H.current=J,U.current=A.clientX}}))})})})})})})});qs.displayName=ae;var Jf="MenuGroup",Vn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(Y.div,{role:"group",...r,ref:t})});Vn.displayName=Jf;var Qf="MenuLabel",Xs=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(Y.div,{...r,ref:t})});Xs.displayName=Qf;var Nt="MenuItem",Nr="menu.itemSelect",Bt=i.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,a=i.useRef(null),c=nt(Nt,e.__scopeMenu),l=Hn(Nt,e.__scopeMenu),d=q(t,a),u=i.useRef(!1),f=()=>{const p=a.current;if(!n&&p){const g=new CustomEvent(Nr,{bubbles:!0,cancelable:!0});p.addEventListener(Nr,v=>r?.(v),{once:!0}),Pr(p,g),g.defaultPrevented?u.current=!1:c.onClose()}};return s.jsx(Zs,{...o,ref:d,disabled:n,onClick:L(e.onClick,f),onPointerDown:p=>{e.onPointerDown?.(p),u.current=!0},onPointerUp:L(e.onPointerUp,p=>{u.current||p.currentTarget?.click()}),onKeyDown:L(e.onKeyDown,p=>{const g=l.searchRef.current!=="";n||g&&p.key===" "||pn.includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})})});Bt.displayName=Nt;var Zs=i.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...a}=e,c=Hn(Nt,n),l=Vs(n),d=i.useRef(null),u=q(t,d),[f,p]=i.useState(!1),[g,v]=i.useState("");return i.useEffect(()=>{const h=d.current;h&&v((h.textContent??"").trim())},[a.children]),s.jsx(qe.ItemSlot,{scope:n,disabled:r,textValue:o??g,children:s.jsx(Lf,{asChild:!0,...l,focusable:!r,children:s.jsx(Y.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...a,ref:u,onPointerMove:L(e.onPointerMove,Xe(h=>{r?c.onItemLeave(h):(c.onItemEnter(h),h.defaultPrevented||h.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:L(e.onPointerLeave,Xe(h=>c.onItemLeave(h))),onFocus:L(e.onFocus,()=>p(!0)),onBlur:L(e.onBlur,()=>p(!1))})})})}),ep="MenuCheckboxItem",Js=i.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return s.jsx(ra,{scope:e.__scopeMenu,checked:n,children:s.jsx(Bt,{role:"menuitemcheckbox","aria-checked":St(n)?"mixed":n,...o,ref:t,"data-state":Kn(n),onSelect:L(o.onSelect,()=>r?.(St(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Js.displayName=ep;var Qs="MenuRadioGroup",[tp,np]=Pe(Qs,{value:void 0,onValueChange:()=>{}}),ea=i.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,a=ve(r);return s.jsx(tp,{scope:e.__scopeMenu,value:n,onValueChange:a,children:s.jsx(Vn,{...o,ref:t})})});ea.displayName=Qs;var ta="MenuRadioItem",na=i.forwardRef((e,t)=>{const{value:n,...r}=e,o=np(ta,e.__scopeMenu),a=n===o.value;return s.jsx(ra,{scope:e.__scopeMenu,checked:a,children:s.jsx(Bt,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":Kn(a),onSelect:L(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});na.displayName=ta;var Gn="MenuItemIndicator",[ra,rp]=Pe(Gn,{checked:!1}),oa=i.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,a=rp(Gn,n);return s.jsx(we,{present:r||St(a.checked)||a.checked===!0,children:s.jsx(Y.span,{...o,ref:t,"data-state":Kn(a.checked)})})});oa.displayName=Gn;var op="MenuSeparator",sa=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(Y.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});sa.displayName=op;var sp="MenuArrow",aa=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=$t(n);return s.jsx(qo,{...o,...r,ref:t})});aa.displayName=sp;var ap="MenuSub",[Mm,ia]=Pe(ap),We="MenuSubTrigger",ca=i.forwardRef((e,t)=>{const n=_e(We,e.__scopeMenu),r=nt(We,e.__scopeMenu),o=ia(We,e.__scopeMenu),a=Hn(We,e.__scopeMenu),c=i.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:d}=a,u={__scopeMenu:e.__scopeMenu},f=i.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return i.useEffect(()=>f,[f]),i.useEffect(()=>{const p=l.current;return()=>{window.clearTimeout(p),d(null)}},[l,d]),s.jsx(zn,{asChild:!0,...u,children:s.jsx(Zs,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":ua(n.open),...e,ref:Fr(t,o.onTriggerChange),onClick:p=>{e.onClick?.(p),!(e.disabled||p.defaultPrevented)&&(p.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:L(e.onPointerMove,Xe(p=>{a.onItemEnter(p),!p.defaultPrevented&&!e.disabled&&!n.open&&!c.current&&(a.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:L(e.onPointerLeave,Xe(p=>{f();const g=n.content?.getBoundingClientRect();if(g){const v=n.content?.dataset.side,h=v==="right",m=h?-5:5,x=g[h?"left":"right"],b=g[h?"right":"left"];a.onPointerGraceIntentChange({area:[{x:p.clientX+m,y:p.clientY},{x,y:g.top},{x:b,y:g.top},{x:b,y:g.bottom},{x,y:g.bottom}],side:v}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(p),p.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:L(e.onKeyDown,p=>{const g=a.searchRef.current!=="";e.disabled||g&&p.key===" "||Bf[r.dir].includes(p.key)&&(n.onOpenChange(!0),n.content?.focus(),p.preventDefault())})})})});ca.displayName=We;var la="MenuSubContent",da=i.forwardRef((e,t)=>{const n=Ks(ae,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=_e(ae,e.__scopeMenu),c=nt(ae,e.__scopeMenu),l=ia(la,e.__scopeMenu),d=i.useRef(null),u=q(t,d);return s.jsx(qe.Provider,{scope:e.__scopeMenu,children:s.jsx(we,{present:r||a.open,children:s.jsx(qe.Slot,{scope:e.__scopeMenu,children:s.jsx(Wn,{id:l.contentId,"aria-labelledby":l.triggerId,...o,ref:u,align:"start",side:c.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{c.isUsingKeyboardRef.current&&d.current?.focus(),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:L(e.onFocusOutside,f=>{f.target!==l.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:L(e.onEscapeKeyDown,f=>{c.onClose(),f.preventDefault()}),onKeyDown:L(e.onKeyDown,f=>{const p=f.currentTarget.contains(f.target),g=zf[c.dir].includes(f.key);p&&g&&(a.onOpenChange(!1),l.trigger?.focus(),f.preventDefault())})})})})})});da.displayName=la;function ua(e){return e?"open":"closed"}function St(e){return e==="indeterminate"}function Kn(e){return St(e)?"indeterminate":e?"checked":"unchecked"}function ip(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function cp(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function lp(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let c=cp(e,Math.max(a,0));o.length===1&&(c=c.filter(u=>u!==n));const d=c.find(u=>u.toLowerCase().startsWith(o.toLowerCase()));return d!==n?d:void 0}function dp(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,c=t.length-1;a<t.length;c=a++){const l=t[a],d=t[c],u=l.x,f=l.y,p=d.x,g=d.y;f>r!=g>r&&n<(p-u)*(r-f)/(g-f)+u&&(o=!o)}return o}function up(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return dp(n,t)}function Xe(e){return t=>t.pointerType==="mouse"?e(t):void 0}var fp=Gs,pp=zn,mp=Ys,hp=qs,gp=Vn,vp=Xs,xp=Bt,bp=Js,wp=ea,yp=na,Cp=oa,Np=sa,Sp=aa,Ep=ca,Rp=da,zt="DropdownMenu",[jp,Dm]=je(zt,[Ws]),te=Ws(),[Ap,fa]=jp(zt),pa=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:a,onOpenChange:c,modal:l=!0}=e,d=te(t),u=i.useRef(null),[f,p]=jt({prop:o,defaultProp:a??!1,onChange:c,caller:zt});return s.jsx(Ap,{scope:t,triggerId:Ae(),triggerRef:u,contentId:Ae(),open:f,onOpenChange:p,onOpenToggle:i.useCallback(()=>p(g=>!g),[p]),modal:l,children:s.jsx(fp,{...d,open:f,onOpenChange:p,dir:r,modal:l,children:n})})};pa.displayName=zt;var ma="DropdownMenuTrigger",ha=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=fa(ma,n),c=te(n);return s.jsx(pp,{asChild:!0,...c,children:s.jsx(Y.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:Fr(t,a.triggerRef),onPointerDown:L(e.onPointerDown,l=>{!r&&l.button===0&&l.ctrlKey===!1&&(a.onOpenToggle(),a.open||l.preventDefault())}),onKeyDown:L(e.onKeyDown,l=>{r||(["Enter"," "].includes(l.key)&&a.onOpenToggle(),l.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())})})})});ha.displayName=ma;var Mp="DropdownMenuPortal",ga=e=>{const{__scopeDropdownMenu:t,...n}=e,r=te(t);return s.jsx(mp,{...r,...n})};ga.displayName=Mp;var va="DropdownMenuContent",xa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=fa(va,n),a=te(n),c=i.useRef(!1);return s.jsx(hp,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...r,ref:t,onCloseAutoFocus:L(e.onCloseAutoFocus,l=>{c.current||o.triggerRef.current?.focus(),c.current=!1,l.preventDefault()}),onInteractOutside:L(e.onInteractOutside,l=>{const d=l.detail.originalEvent,u=d.button===0&&d.ctrlKey===!0,f=d.button===2||u;(!o.modal||f)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});xa.displayName=va;var Dp="DropdownMenuGroup",Pp=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(gp,{...o,...r,ref:t})});Pp.displayName=Dp;var _p="DropdownMenuLabel",ba=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(vp,{...o,...r,ref:t})});ba.displayName=_p;var Tp="DropdownMenuItem",wa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(xp,{...o,...r,ref:t})});wa.displayName=Tp;var Ip="DropdownMenuCheckboxItem",ya=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(bp,{...o,...r,ref:t})});ya.displayName=Ip;var Op="DropdownMenuRadioGroup",kp=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(wp,{...o,...r,ref:t})});kp.displayName=Op;var Lp="DropdownMenuRadioItem",Ca=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(yp,{...o,...r,ref:t})});Ca.displayName=Lp;var Fp="DropdownMenuItemIndicator",Na=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(Cp,{...o,...r,ref:t})});Na.displayName=Fp;var $p="DropdownMenuSeparator",Sa=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(Np,{...o,...r,ref:t})});Sa.displayName=$p;var Bp="DropdownMenuArrow",zp=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(Sp,{...o,...r,ref:t})});zp.displayName=Bp;var Up="DropdownMenuSubTrigger",Ea=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(Ep,{...o,...r,ref:t})});Ea.displayName=Up;var Hp="DropdownMenuSubContent",Ra=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=te(n);return s.jsx(Rp,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ra.displayName=Hp;var Wp=pa,Vp=ha,Gp=ga,ja=xa,Aa=ba,Ma=wa,Da=ya,Pa=Ca,_a=Na,Ta=Sa,Ia=Ea,Oa=Ra;const Kp=Wp,Yp=Vp,qp=i.forwardRef(({className:e,inset:t,children:n,...r},o)=>s.jsxs(Ia,{ref:o,className:M("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,s.jsx(zr,{className:"ml-auto h-4 w-4"})]}));qp.displayName=Ia.displayName;const Xp=i.forwardRef(({className:e,...t},n)=>s.jsx(Oa,{ref:n,className:M("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));Xp.displayName=Oa.displayName;const ka=i.forwardRef(({className:e,sideOffset:t=4,...n},r)=>s.jsx(Gp,{children:s.jsx(ja,{ref:r,sideOffset:t,className:M("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));ka.displayName=ja.displayName;const mn=i.forwardRef(({className:e,inset:t,...n},r)=>s.jsx(Ma,{ref:r,className:M("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));mn.displayName=Ma.displayName;const Zp=i.forwardRef(({className:e,children:t,checked:n,...r},o)=>s.jsxs(Da,{ref:o,className:M("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(_a,{children:s.jsx($r,{className:"h-4 w-4"})})}),t]}));Zp.displayName=Da.displayName;const Jp=i.forwardRef(({className:e,children:t,...n},r)=>s.jsxs(Pa,{ref:r,className:M("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(_a,{children:s.jsx(mi,{className:"h-2 w-2 fill-current"})})}),t]}));Jp.displayName=Pa.displayName;const La=i.forwardRef(({className:e,inset:t,...n},r)=>s.jsx(Aa,{ref:r,className:M("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));La.displayName=Aa.displayName;const hn=i.forwardRef(({className:e,...t},n)=>s.jsx(Ta,{ref:n,className:M("-mx-1 my-1 h-px bg-muted",e),...t}));hn.displayName=Ta.displayName;function Qp({className:e}){const t=Mr(),{logout:n}=Or(),{user:r}=ue(),o=l=>l.split(" ").map(d=>d[0]).join("").toUpperCase().slice(0,2),a=()=>{t({to:"/user"})},c=()=>{n(),t({to:"/auth/login"})};return s.jsxs(Kp,{children:[s.jsx(Yp,{asChild:!0,children:s.jsx(Q,{variant:"ghost",size:"sm",className:`h-8 w-8 rounded-full p-0 ${e}`,children:s.jsxs(_r,{className:"h-8 w-8",children:[s.jsx(Tr,{src:r?.avatar||"",alt:r?.name||"用户"}),s.jsx(Ir,{className:"text-xs",children:o(r?.name||"用户")})]})})}),s.jsxs(ka,{align:"end",className:"w-56",children:[s.jsx(La,{className:"font-normal",children:s.jsx("div",{className:"flex flex-col space-y-1",children:s.jsx("p",{className:"text-sm font-medium leading-none",children:r?.name||"用户"})})}),s.jsx(hn,{}),s.jsxs(mn,{onClick:a,children:[s.jsx(ai,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"账号管理"})]}),s.jsx(hn,{}),s.jsxs(mn,{onClick:c,children:[s.jsx(Ci,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"退出登录"})]})]})]})}function em({references:e,messageId:t}){const[n,r]=i.useState(new Set),o=a=>{r(c=>{const l=new Set(c);return l.has(a)?l.delete(a):l.add(a),l})};return!e||e.length===0?null:s.jsxs("div",{className:"mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx(xi,{className:"h-4 w-4 text-blue-600"}),s.jsx("span",{className:"text-sm font-medium text-blue-800",children:"参考文档"}),s.jsxs(ii,{variant:"secondary",className:"text-xs",children:[e.length," 个文档"]})]}),s.jsx("div",{className:"space-y-2",children:e.map((a,c)=>s.jsxs("div",{className:"border border-blue-200 rounded-md bg-white",children:[s.jsxs(Q,{variant:"ghost",className:"w-full justify-between p-3 h-auto text-left",onClick:()=>o(c),children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"font-medium text-sm text-gray-900",children:a.doc_name}),a.page_number&&a.page_number.length>0&&s.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["页码: ",a.page_number.join(", ")]})]}),n.has(c)?s.jsx(Br,{className:"h-4 w-4 text-gray-500"}):s.jsx(zr,{className:"h-4 w-4 text-gray-500"})]}),n.has(c)&&s.jsxs("div",{className:"px-3 pb-3 border-t border-gray-100",children:[a.title&&s.jsx("div",{className:"text-sm font-medium text-blue-700 mb-2",children:a.title}),s.jsx("div",{className:"text-sm text-gray-700 leading-relaxed",children:a.text}),a.doc_id&&s.jsxs("div",{className:"text-xs text-gray-400 mt-2",children:["文档ID: ",a.doc_id]})]})]},c))})]})}let gn=null,Ve=null;const Fa=async()=>{if(typeof window>"u")throw new Error("File reading is only supported on the client side");gn||(gn=await Yn(()=>import("./index-DbVZ_fy7.js").then(e=>e.i),__vite__mapDeps([0,1]))),Ve||(Ve=await Yn(()=>import("./pdf-CtA8PhPd.js"),[]),Ve.GlobalWorkerOptions.workerSrc=`//cdnjs.cloudflare.com/ajax/libs/pdf.js/${Ve.version}/pdf.worker.min.js`)};async function tm(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=o=>{const a=o.target?.result;t(a)},r.onerror=()=>n(new Error("Failed to read text file")),r.readAsText(e,"utf-8")})}async function nm(e){return await Fa(),new Promise((t,n)=>{const r=new FileReader;r.onload=async o=>{try{const a=o.target?.result,c=await gn.extractRawText({arrayBuffer:a});t(c.value)}catch(a){n(new Error(`Failed to read Word file: ${a}`))}},r.onerror=()=>n(new Error("Failed to read Word file")),r.readAsArrayBuffer(e)})}async function rm(e){return await Fa(),new Promise((t,n)=>{const r=new FileReader;r.onload=async o=>{try{const a=o.target?.result,c=await Ve.getDocument({data:a}).promise;let l="";for(let d=1;d<=c.numPages;d++){const p=(await(await c.getPage(d)).getTextContent()).items.map(g=>g.str).join(" ");l+=`Page ${d}:
${p}

`}t(l)}catch(a){n(new Error(`Failed to read PDF file: ${a}`))}},r.onerror=()=>n(new Error("Failed to read PDF file")),r.readAsArrayBuffer(e)})}async function om(e){const n=e.name.toLowerCase().split(".").pop();console.log(`Reading file: ${e.name} (${e.size} bytes)`),console.log(`File type: ${e.type}`),console.log(`File extension: ${n}`);try{let r="";if(n==="txt"||e.type==="text/plain")r=await tm(e);else if(n==="docx"||e.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document")r=await nm(e);else if(n==="pdf"||e.type==="application/pdf")r=await rm(e);else throw new Error(`Unsupported file type: ${n}. Only TXT, DOCX, and PDF files are supported.`);return console.log(`Successfully read file content (${r.length} characters)`),console.log("File content preview:",r.substring(0,200)+"..."),r}catch(r){throw console.error("Error reading file:",r),r}}function sm(e){const n=e.name.toLowerCase().split(".").pop(),r=["txt","docx","pdf"],o=["text/plain","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/pdf"];return r.includes(n||"")||o.includes(e.type)}function am(){return s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[s.jsx("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:s.jsx("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),s.jsx("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:s.jsx("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),s.jsx("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:s.jsx("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}function Sr({variant:e="received",layout:t="default",className:n,children:r}){return s.jsx("div",{className:M("flex items-start gap-2 mb-4",e==="sent"&&"flex-row-reverse",n),children:r})}function Er({variant:e="received",isLoading:t,className:n,children:r}){return s.jsx("div",{className:M("rounded-lg p-3",e==="sent"?"bg-primary text-primary-foreground":"bg-muted",n),children:t?s.jsx("div",{className:"flex items-center space-x-2",children:s.jsx(am,{})}):r})}function Rr({src:e,fallback:t="AI",className:n}){return s.jsxs(_r,{className:M("h-8 w-8",n),children:[e&&s.jsx(Tr,{src:e}),s.jsx(Ir,{children:t})]})}function im({icon:e,onClick:t,className:n}){return s.jsx(Q,{variant:"ghost",size:"icon",className:M("h-6 w-6",n),onClick:t,children:e})}function cm({className:e,children:t}){return s.jsx("div",{className:M("flex items-center gap-1 mt-2",e),children:t})}function lm({children:e,redirectTo:t="/auth/login"}){const{isAuthenticated:n,isLoading:r}=Or(),o=Mr();return de.useEffect(()=>{if(!r&&!n){const a=window.location.pathname;a!==t&&localStorage.setItem("redirectAfterLogin",a),o({to:t})}},[r,n,o,t]),r?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsxs("div",{className:"text-center",children:[s.jsx(nn,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),s.jsx("p",{className:"text-gray-600",children:"正在验证登录状态..."})]})}):n?s.jsx(s.Fragment,{children:e}):s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsxs("div",{className:"text-center",children:[s.jsx(nn,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),s.jsx("p",{className:"text-gray-600",children:"正在跳转到登录页面..."})]})})}const dm=({className:e})=>s.jsx("svg",{height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",className:e,children:s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.4697 13.5303L13 14.0607L14.0607 13L13.5303 12.4697L9.06065 7.99999L13.5303 3.53032L14.0607 2.99999L13 1.93933L12.4697 2.46966L7.99999 6.93933L3.53032 2.46966L2.99999 1.93933L1.93933 2.99999L2.46966 3.53032L6.93933 7.99999L2.46966 12.4697L1.93933 13L2.99999 14.0607L3.53032 13.5303L7.99999 9.06065L12.4697 13.5303Z"})}),um=()=>s.jsx("svg",{height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",className:"fill-gray-1000",children:s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.5 8C13.5 4.96643 11.0257 2.5 7.96452 2.5C5.42843 2.5 3.29365 4.19393 2.63724 6.5H5.25H6V8H5.25H0.75C0.335787 8 0 7.66421 0 7.25V2.75V2H1.5V2.75V5.23347C2.57851 2.74164 5.06835 1 7.96452 1C11.8461 1 15 4.13001 15 8C15 11.87 11.8461 15 7.96452 15C5.62368 15 3.54872 13.8617 2.27046 12.1122L1.828 11.5066L3.03915 10.6217L3.48161 11.2273C4.48831 12.6051 6.12055 13.5 7.96452 13.5C11.0257 13.5 13.5 11.0336 13.5 8Z"})});let tn=null,fm=0;const X={toasts:[],listeners:new Set,add(e,t,n,r,o,a){const c=fm++,l={id:c,text:e,preserve:n,action:r,onAction:o,onUndoAction:a,type:t};if(!l.preserve){l.remaining=3e3,l.start=Date.now();const d=()=>{this.toasts=this.toasts.filter(u=>u.id!==c),this.notify()};l.timeout=setTimeout(d,l.remaining),l.pause=()=>{l.timeout&&(clearTimeout(l.timeout),l.timeout=void 0,l.remaining-=Date.now()-l.start)},l.resume=()=>{l.timeout||(l.start=Date.now(),l.timeout=setTimeout(d,l.remaining))}}this.toasts.push(l),this.notify()},remove(e){X.toasts=X.toasts.filter(t=>t.id!==e),X.notify()},subscribe(e){return X.listeners.add(e),()=>{X.listeners.delete(e)}},notify(){X.listeners.forEach(e=>e())}},pm=()=>{const[e,t]=i.useState([]),[n,r]=i.useState([]),[o,a]=i.useState(!1),c=h=>m=>{m&&h.measuredHeight==null&&(h.measuredHeight=m.getBoundingClientRect().height,X.notify())};i.useEffect(()=>(t([...X.toasts]),X.subscribe(()=>{t([...X.toasts])})),[]),i.useEffect(()=>{const h=e.filter(m=>!n.includes(m.id)).map(m=>m.id);h.length>0&&requestAnimationFrame(()=>{r(m=>[...m,...h])})},[e]);const d=Math.max(0,e.length-3),u=(h,m)=>{if(h===m-1)return"none";const x=m-1-h;let b=e[m-1]?.measuredHeight||63;for(let C=m-1;C>h;C--)o?b+=(e[C-1]?.measuredHeight||63)+10:b+=20;const w=-x,y=o?1:1-.05*x;return`translate3d(0, calc(100% - ${b}px), ${w}px) scale(${y})`},f=()=>{a(!0),X.toasts.forEach(h=>h.pause?.())},p=()=>{a(!1),X.toasts.forEach(h=>h.resume?.())},v=e.slice(d).reduce((h,m)=>h+(m.measuredHeight??63),0);return s.jsx("div",{className:"fixed bottom-4 right-4 z-[9999] pointer-events-none w-[420px]",style:{height:v},children:s.jsx("div",{className:"relative pointer-events-auto w-full",style:{height:v},onMouseEnter:f,onMouseLeave:p,children:e.map((h,m)=>{const x=m>=d;return s.jsx("div",{ref:c(h),className:Ga("absolute right-0 bottom-0 shadow-menu rounded-xl leading-[21px] p-4 h-fit",{message:"bg-geist-background text-gray-1000",success:"bg-blue-700 text-contrast-fg",warning:"bg-amber-800 text-gray-1000 dark:text-gray-100",error:"bg-red-800 text-contrast-fg"}[h.type],x?"opacity-100":"opacity-0",m<d&&"pointer-events-none"),style:{width:420,transition:"all .35s cubic-bezier(.25,.75,.6,.98)",transform:n.includes(h.id)?u(m,e.length):"translate3d(0, 100%, 150px) scale(1)"},children:s.jsxs("div",{className:"flex flex-col items-center justify-between text-[.875rem]",children:[s.jsxs("div",{className:"w-full h-full flex items-center justify-between gap-4",children:[s.jsx("span",{children:h.text}),!h.action&&s.jsxs("div",{className:"flex gap-1",children:[h.onUndoAction&&s.jsx(Q,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>{h.onUndoAction?.(),X.remove(h.id)},children:s.jsx(um,{})}),s.jsx(Q,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>X.remove(h.id),children:s.jsx(dm,{className:{message:"fill-gray-1000",success:"fill-contrast-fg",warning:"fill-gray-1000 dark:fill-gray-100",error:"fill-contrast-fg"}[h.type]})})]})]}),h.action&&s.jsxs("div",{className:"w-full flex items-center justify-end gap-2",children:[s.jsx(Q,{variant:"ghost",size:"sm",onClick:()=>X.remove(h.id),children:"Dismiss"}),s.jsx(Q,{variant:"default",size:"sm",onClick:()=>{h?.onAction&&h?.onAction(),X.remove(h.id)},children:h.action})]})]})},h.id)})})})},ft=()=>{if(tn)return;const e=document.createElement("div");e.className="fixed bottom-4 right-4 z-[9999]",document.body.appendChild(e),tn=Wa.createRoot(e),tn.render(s.jsx(pm,{}))},mm=()=>({message:i.useCallback(({text:e,preserve:t,action:n,onAction:r,onUndoAction:o})=>{ft(),X.add(e,"message",t,n,r,o)},[]),success:i.useCallback(e=>{ft(),X.add(e,"success")},[]),warning:i.useCallback(e=>{ft(),X.add(e,"warning")},[]),error:i.useCallback(e=>{ft(),X.add(e,"error")},[])}),jr=["无忧问答","无忧分析师","无忧计算师"];function hm(){const[e,t]=i.useState(jr[0]),[n,r]=i.useState(!1),[o,a]=i.useState(null),[c,l]=i.useState(!1),[d,u]=i.useState(!1),{getCurrentConversation:f,addMessage:p,currentConversationId:g,createConversation:v}=rn(),h=mm(),x=f()?.messages||[],b=i.useCallback(j=>{t(j),a(null);const{setCurrentConversation:I}=rn.getState();I(null),console.log(`切换到 ${j} 模式`)},[]),w="sk-5ff58b88af7343b7bbb388079e1442f2",y="622fbd2ef57c413baafa29527d205414",C="38669993697942e6a8ac1a9f1aa591e0",S="680919cbac364a80b24306137e5debeb",[R,N]=i.useState(null),[D,E]=i.useState(""),[T,O]=i.useState([]),W=i.useCallback(j=>{N(j),E(""),O([])},[]),H=j=>{O(j),console.log("=== 格式化显示结果 ==="),j.forEach((I,_)=>{console.log(`%c问题 ${_+1}:`,"color: #e74c3c; font-weight: bold;"),console.log(`%c原文: ${I.origin}`,"color: #3498db;"),console.log(`%c问题描述: ${I.issueDes}`,"color: #f39c12;"),console.log(`%c改进建议: ${I.suggestion}`,"color: #27ae60;"),console.log(`%c依据: ${I.reason}`,"color: #9b59b6;"),console.log("---")})},U=async j=>{try{const{user:I}=ue.getState();if(I&&I.id){console.log("=== 数据库更新 ==="),console.log(`Token 使用量: ${j}`),console.log("请求次数: +1");let _;I.dingTalkUnionId?_=await oi(I.dingTalkUnionId,j):_=await si(I.id,j),_?(console.log("用户token使用量更新成功:",_),ue.getState().setUser(_)):console.error("更新用户token使用量失败")}else console.warn("用户信息不完整，无法更新数据库")}catch(I){console.error("更新数据库失败:",I)}},G=async(j,I)=>{try{const _=await fetch(`https://dashscope.aliyuncs.com/api/v1/apps/${C}/completion`,{method:"POST",headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"},body:JSON.stringify({input:{prompt:j},parameters:{},debug:{}})});if(!_.ok)throw new Error(`API 请求失败: ${_.status} ${_.statusText}`);const V=await _.json();if(console.log("=== DashScope API 响应 ==="),console.log("完整响应数据:"),console.log(JSON.stringify(V,null,2)),V.output&&V.output.text)try{const F=V.output.text.replace(/```json\n?/,"").replace(/\n?```$/,""),ne=JSON.parse(F);if(console.log("=== 解析后的结果 ==="),console.log(ne),H(ne),await z(I,j,V.output.text,ne),u(!1),V.usage&&V.usage.models&&V.usage.models[0]){const He=V.usage.models[0].input_tokens+V.usage.models[0].output_tokens;setTimeout(()=>{U(He)},0)}}catch(F){console.error("解析 JSON 失败:",F),console.log("原始 text 内容:",V.output.text),u(!1)}console.log("=== API 响应完成 ===")}catch(_){throw console.error("API 请求失败:",_),_}},z=async(j,I,_,V)=>{try{let F=g;if(!F){const rt=`分析文件: ${j}`;F=await v(rt,"无忧分析师"),console.log(`创建新对话: ${rt}`)}const ne=`上传文件进行分析：${j}

文件内容摘要：${I.substring(0,200)}${I.length>200?"...":""}`;await p(F,ne,"user");const He=`文件分析完成，共发现 ${V.length} 个问题：

${_}`;await p(F,He,"assistant"),console.log("分析结果已保存到对话历史")}catch(F){console.error("保存分析结果到对话历史失败:",F)}},$=()=>{r(!n)},k=async(j,I)=>{console.log("=== API调用调试信息 ==="),console.log("API_KEY:",w),console.log("APP_ID:",y),console.log("环境变量 VITE_DASHSCOPE_API_KEY:","sk-5ff58b88af7343b7bbb388079e1442f2"),console.log("环境变量 VITE_YOUR_APP_ID:","622fbd2ef57c413baafa29527d205414"),console.log("========================");const _=`https://dashscope.aliyuncs.com/api/v1/apps/${y}/completion`,V={input:{prompt:j},parameters:{},debug:{}};I&&(V.input.session_id=I);const F=await fetch(_,{method:"POST",headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"},body:JSON.stringify(V)});if(!F.ok)throw new Error(`API请求失败: ${F.status} ${F.statusText}`);return await F.json()},A=async(j,I)=>{console.log("=== 计算API调用调试信息 ==="),console.log("API_KEY:",w),console.log("CALCULATE_APP_ID:",S),console.log("环境变量 VITE_CALCULATE_APP_ID:","680919cbac364a80b24306137e5debeb"),console.log("========================");const _=`https://dashscope.aliyuncs.com/api/v1/apps/${S}/completion`,V={input:{prompt:j},parameters:{},debug:{}};I&&(V.input.session_id=I);const F=await fetch(_,{method:"POST",headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"},body:JSON.stringify(V)});if(!F.ok)throw new Error(`计算API请求失败: ${F.status} ${F.statusText}`);return await F.json()},P=async j=>{let I=g;if(!I)try{const _=j.length>8?j.substring(0,8)+"...":j;I=await v(_,e),console.log(`创建新对话: ${_}`)}catch(_){console.error("创建对话失败:",_);return}try{await p(I,j,"user"),l(!0);let _;e==="无忧计算师"?_=await A(j,o||void 0):_=await k(j,o||void 0),_.output.session_id&&a(_.output.session_id);let V=_.output.text,F=[];if(e==="无忧问答"&&_.output.doc_references&&(F=_.output.doc_references,console.log("文档引用:",F)),l(!1),await p(I,V,"assistant",F),_.usage&&_.usage.models&&_.usage.models[0]){const ne=_.usage.models[0].input_tokens+_.usage.models[0].output_tokens;setTimeout(()=>{U(ne)},0)}}catch(_){console.error("API调用失败:",_),l(!1);const V=`抱歉，服务暂时不可用。请检查API配置或稍后重试。错误信息: ${_ instanceof Error?_.message:"未知错误"}`;await p(I,V,"assistant")}},B=async j=>{try{await navigator.clipboard.writeText(j),h.success("复制成功")}catch(I){console.error("复制失败:",I);try{const _=document.createElement("textarea");_.value=j,document.body.appendChild(_),_.select(),document.execCommand("copy"),document.body.removeChild(_),h.success("复制成功")}catch(_){console.error("降级复制方案也失败:",_),h.error("复制失败，请手动选择文本复制")}}},J=async()=>{if(!R){alert("请先选择一个文件");return}if(!sm(R)){alert("不支持的文件类型。请选择 TXT、PDF 或 DOCX 文件。");return}try{u(!0),console.log("=== 开始读取文件 ==="),console.log("文件名:",R.name),console.log("文件大小:",R.size,"bytes"),console.log("文件类型:",R.type);const j=await om(R);console.log("=== 文件内容 ==="),console.log("内容长度:",j.length,"字符"),console.log("文件内容:"),console.log(j),console.log("=== 文件读取完成 ==="),E(j),console.log("=== 开始发送到 DashScope API ==="),await G(j,R.name)}catch(j){console.error("处理失败:",j),u(!1),E(""),alert(`处理失败: ${j instanceof Error?j.message:"未知错误"}`)}};return s.jsx(Sf,{isCollapsed:n,onToggle:$,currentMode:e,children:s.jsxs("div",{className:"h-screen bg-gray-50 flex flex-col",children:[s.jsx("div",{className:"bg-white h-16 border-b border-gray-200 px-6 py-3 flex-shrink-0",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Q,{variant:"ghost",size:"sm",onClick:$,className:"h-8 w-8 p-0",title:n?"展开侧边栏 (Ctrl+B)":"收起侧边栏 (Ctrl+B)",children:n?s.jsx(Di,{className:"h-4 w-4"}):s.jsx(Ai,{className:"h-4 w-4"})}),s.jsx("div",{className:"flex justify-center flex-1",children:s.jsx("div",{className:"flex w-fit rounded-full bg-muted p-1",children:jr.map(j=>s.jsx(ic,{text:j,selected:e===j,setSelected:b,discount:j==="无忧分析师"},j))})}),s.jsx(Qp,{className:"ml-4"})]})}),s.jsx("main",{className:"flex-1 flex flex-col min-h-0 overflow-hidden",children:e==="无忧问答"||e==="无忧计算师"?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"flex-1 overflow-y-auto p-6 pb-0 min-h-0",children:x.length===0?s.jsxs("div",{className:"text-center text-gray-500 mt-20",children:[e==="无忧问答"?s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-lg mb-2",children:"👋 欢迎使用无忧问答"}),s.jsx("p",{children:"开始对话，快速检索石化油储行业"})]}):s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-lg mb-2",children:"🧮 欢迎使用无忧计算师"}),s.jsx("p",{children:"开始对话，进行智能计算和分析"})]}),o&&s.jsxs("p",{className:"text-sm mt-4 text-blue-600",children:["🔗 多轮对话已启用 (会话ID: ",o.slice(0,8),"...)"]})]}):s.jsxs("div",{className:"max-w-4xl mx-auto space-y-4",children:[x.map(j=>{const I=j.role==="user"?"sent":"received";return s.jsxs("div",{className:"space-y-2",children:[j.role==="assistant"&&j.docReferences&&j.docReferences.length>0&&s.jsx(em,{references:j.docReferences,messageId:j.id}),s.jsxs(Sr,{variant:I,children:[s.jsx(Rr,{src:I==="sent"?"/user.jpg":"/chatlogo.png",fallback:I==="sent"?"用户":"AI"}),s.jsxs("div",{className:"flex-1",children:[s.jsx(Er,{variant:I,children:j.content}),j.role==="assistant"&&s.jsx(cm,{children:s.jsx(im,{icon:s.jsx(gi,{className:"h-3 w-3"}),onClick:()=>B(j.content)})})]})]})]},j.id)}),c&&s.jsx("div",{className:"space-y-2",children:s.jsxs(Sr,{variant:"received",children:[s.jsx(Rr,{src:"/chatlogo.png",fallback:"AI"}),s.jsx("div",{className:"flex-1",children:s.jsx(Er,{variant:"received",children:s.jsx("div",{className:"flex items-center justify-center py-2",children:s.jsx(qn,{})})})})]})})]})}),s.jsx("div",{className:"w-full flex justify-center flex-shrink-0 p-6 pt-0",children:s.jsx("div",{className:"w-full max-w-3xl",children:s.jsx(oc,{onSend:P,showSuggestedActions:e!=="无忧计算师"})})})]}):s.jsx(s.Fragment,{children:s.jsx("div",{className:"flex-1 flex flex-col p-6 min-h-0 overflow-hidden",children:T.length===0?s.jsxs("div",{className:"flex-1 flex flex-col justify-center items-center",children:[s.jsxs("div",{className:"text-center text-gray-500 mb-6",children:[s.jsx("p",{className:"text-lg mb-2",children:"📄 无忧分析师"}),s.jsx("p",{children:"上传文件进行智能分析和优化建议"})]}),s.jsxs("div",{className:"w-full max-w-md space-y-4",children:[s.jsx(ac,{onFileSelect:W,fileContent:D}),s.jsx(Q,{onClick:J,disabled:!R||d,className:"w-full",children:d?s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx(qn,{}),s.jsx("span",{children:"分析中..."})]}):"分析文件内容"})]})]}):s.jsxs("div",{className:"w-full max-w-4xl mx-auto h-full flex flex-col",children:[s.jsxs("div",{className:"mb-6 flex items-center justify-between flex-shrink-0",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"📋 分析结果"}),s.jsx(Q,{onClick:()=>{O([]),E(""),N(null)},variant:"outline",size:"sm",children:"重新分析"})]}),s.jsx("div",{className:"flex-1 overflow-y-auto space-y-6 pr-2",children:T.map((j,I)=>s.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:[s.jsx("div",{className:"mb-4",children:s.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:["问题 ",I+1]})}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"text-sm font-semibold text-blue-700 mb-2",children:"📝 原文内容"}),s.jsx("p",{className:"text-gray-700 bg-blue-50 p-3 rounded-md border-l-4 border-blue-400",children:j.origin})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-sm font-semibold text-orange-700 mb-2",children:"⚠️ 问题描述"}),s.jsx("p",{className:"text-gray-700 bg-orange-50 p-3 rounded-md border-l-4 border-orange-400",children:j.issueDes})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-sm font-semibold text-green-700 mb-2",children:"💡 改进建议"}),s.jsx("p",{className:"text-gray-700 bg-green-50 p-3 rounded-md border-l-4 border-green-400",children:j.suggestion})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-sm font-semibold text-purple-700 mb-2",children:"📚 依据说明"}),s.jsx("p",{className:"text-gray-700 bg-purple-50 p-3 rounded-md border-l-4 border-purple-400",children:j.reason})]})]})]},I))})]})})})})]})})}const Pm=function(){return s.jsx(lm,{children:s.jsx(hm,{})})};export{Pm as component};
