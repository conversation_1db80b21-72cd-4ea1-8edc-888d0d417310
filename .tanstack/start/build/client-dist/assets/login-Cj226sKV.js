import{f as v,r as l,j as e}from"./main-B6mr56sS.js";import{a as N,u as k}from"./use-auth-BT4cMsMh.js";import{c as C}from"./createLucideIcon-3aflogHk.js";import{M as S}from"./mail-BJdA_1fC.js";import{L,E,a as I}from"./lock-Cwc0PnYk.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]],_=C("log-in",M),z=()=>{const i=v(),{login:f}=N(),{setUser:g}=k(),[a,u]=l.useState("email"),[r,b]=l.useState({email:"",password:""}),[c,w]=l.useState(!1),[x,h]=l.useState(!1),[d,o]=l.useState(""),p=n=>{const{name:t,value:s}=n.target;b(m=>({...m,[t]:s})),d&&o("")},y=async n=>{if(n.preventDefault(),!r.email||!r.password){o("请填写邮箱和密码");return}h(!0),o("");try{const t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),s=await t.json();t.ok?(f(s.user,s.token),g(s.user),i.navigate({to:"/"})):o(s.message||"登录失败")}catch(t){console.error("Login error:",t),o("网络错误，请稍后重试")}finally{h(!1)}},j=()=>{const n="dingaikvuvacp2rme0ef",t=encodeURIComponent(window.location.origin+"/auth/dingtalk/callback"),m=`https://login.dingtalk.com/oauth2/auth?response_type=code&client_id=${n}&redirect_uri=${t}&state=dingtalk_login&scope=openid%20corpid&prompt=consent`;window.location.href=m};return e.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1",children:e.jsxs("div",{className:"w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black",children:[e.jsx("div",{className:"flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5",children:e.jsx(_,{className:"w-7 h-7 text-black"})}),e.jsx("h2",{className:"text-2xl font-semibold mb-2 text-center",children:"用户登录"}),e.jsx("p",{className:"text-gray-500 text-sm mb-6 text-center",children:"选择您的登录方式"}),e.jsxs("div",{className:"w-full flex mb-6 bg-gray-100 rounded-xl p-1",children:[e.jsx("button",{onClick:()=>u("email"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition ${a==="email"?"bg-white text-black shadow-sm":"text-gray-600 hover:text-black"}`,children:"邮箱登录"}),e.jsxs("button",{onClick:()=>u("dingtalk"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition relative ${a==="dingtalk"?"bg-white text-black shadow-sm":"text-gray-600 hover:text-black"}`,children:["钉钉登录",e.jsx("span",{className:"absolute -top-2 right-1 px-1.5 py-0.5 bg-red-500 text-white text-xxs rounded-full",style:{fontSize:"0.6rem"},children:"仅限中化兴海内部成员"})]})]}),d&&e.jsx("div",{className:"w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm",children:d}),a==="email"&&e.jsxs("form",{onSubmit:y,className:"w-full space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(S,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",name:"email",placeholder:"邮箱地址",value:r.email,onChange:p,required:!0,className:"w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:c?"text":"password",name:"password",placeholder:"密码",value:r.password,onChange:p,required:!0,className:"w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("button",{type:"button",onClick:()=>w(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:c?e.jsx(E,{className:"w-5 h-5"}):e.jsx(I,{className:"w-5 h-5"})})]}),e.jsx("button",{type:"submit",disabled:x,className:"w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed",children:x?"登录中...":"登录"})]}),a==="dingtalk"&&e.jsxs("button",{onClick:j,className:"w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition mb-6 flex items-center justify-center gap-2",children:[e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"})}),"使用钉钉登录"]}),a==="email"&&e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-gray-500 text-sm",children:["还没有账户？"," ",e.jsx("button",{onClick:()=>i.navigate({to:"/auth/register"}),className:"text-blue-500 hover:text-blue-600 font-medium",children:"立即注册"})]})}),e.jsxs("div",{className:"text-center text-sm text-gray-500 mt-4",children:["登录即表示您同意我们的"," ",e.jsx("a",{href:"/terms",className:"text-blue-600 hover:underline font-medium",children:"服务条款"})," ","和"," ",e.jsx("a",{href:"/privacy",className:"text-blue-600 hover:underline font-medium",children:"隐私政策"})]})]})})},P=function(){return e.jsx(z,{})};export{P as component};
