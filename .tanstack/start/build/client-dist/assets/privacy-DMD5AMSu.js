import{j as s,L as e}from"./main-B6mr56sS.js";import{C as l,a as i,b as c,c as n,d as r}from"./card-CdUhOvNY.js";import{B as x}from"./button-B5f_3GyS.js";import{A as a}from"./arrow-left-BrTwqmNq.js";import"./index-_TRYHs0w.js";import"./createLucideIcon-3aflogHk.js";const b=function(){return s.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[s.jsx(x,{variant:"ghost",className:"mb-4",asChild:!0,children:s.jsxs(e,{to:"/",children:[s.jsx(a,{className:"mr-2 h-4 w-4"}),"返回"]})}),s.jsxs(l,{children:[s.jsxs(i,{children:[s.jsx(c,{className:"text-3xl font-bold",children:"隐私政策"}),s.jsx(n,{className:"text-lg",children:"最后更新日期：2024年12月"})]}),s.jsxs(r,{className:"prose prose-gray max-w-none",children:[s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"1. 信息收集"}),s.jsx("p",{children:"我们收集以下类型的信息："}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsxs("li",{children:[s.jsx("strong",{children:"账户信息："}),"当您注册账户时，我们会收集您的姓名、电子邮件地址和密码"]}),s.jsxs("li",{children:[s.jsx("strong",{children:"使用数据："}),"我们会收集您如何使用我们服务的信息，包括交互记录和偏好设置"]}),s.jsxs("li",{children:[s.jsx("strong",{children:"设备信息："}),"我们可能会收集设备标识符、浏览器类型和IP地址"]})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"2. 信息使用"}),s.jsx("p",{children:"我们使用收集的信息来："}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsx("li",{children:"提供、维护和改进我们的服务"}),s.jsx("li",{children:"个性化您的用户体验"}),s.jsx("li",{children:"与您沟通，包括发送服务更新和通知"}),s.jsx("li",{children:"保护我们的服务和用户的安全"})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"3. 信息共享"}),s.jsx("p",{children:"我们不会出售您的个人信息。我们仅在以下情况下共享信息："}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsx("li",{children:"获得您的明确同意"}),s.jsx("li",{children:"遵守法律义务或响应合法要求"}),s.jsx("li",{children:"保护我们的权利、隐私、安全或财产"}),s.jsx("li",{children:"与服务提供商合作，这些提供商必须保护您的信息"})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"4. 数据安全"}),s.jsx("p",{children:"我们实施适当的技术和组织措施来保护您的个人信息，包括："}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsx("li",{children:"使用加密技术保护数据传输"}),s.jsx("li",{children:"实施访问控制和身份验证机制"}),s.jsx("li",{children:"定期进行安全评估和审计"})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"5. 您的权利"}),s.jsx("p",{children:"根据适用法律，您可能拥有以下权利："}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsx("li",{children:"访问您的个人信息"}),s.jsx("li",{children:"更正不准确的个人信息"}),s.jsx("li",{children:"删除您的个人信息"}),s.jsx("li",{children:"限制或反对处理您的个人信息"}),s.jsx("li",{children:"数据可携带权"})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"6. 数据保留"}),s.jsx("p",{children:"我们仅在实现本隐私政策所述目的所需的时间内保留您的个人信息，除非法律要求或允许更长的保留期。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"7. 第三方链接"}),s.jsx("p",{children:"我们的服务可能包含指向第三方网站或服务的链接。我们对这些第三方的隐私实践不承担责任，建议您查看他们的隐私政策。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"8. 儿童隐私"}),s.jsx("p",{children:"我们的服务不针对13岁以下的儿童。如果我们发现收集了13岁以下儿童的个人信息，我们将采取措施删除这些信息。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"9. 隐私政策的变更"}),s.jsx("p",{children:"我们可能会不时更新本隐私政策。任何变更都将在本页面发布，并在适当情况下通过其他方式通知您。"})]}),s.jsxs("section",{children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"10. 联系我们"}),s.jsx("p",{children:"如果您对本隐私政策有任何疑问或担忧，请通过以下方式联系我们："}),s.jsxs("p",{className:"mt-2",children:["电子邮件：",s.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})]}),s.jsxs("p",{children:["电话：",s.jsx("a",{href:"tel:+861012345678",className:"text-blue-600 hover:underline",children:"+86 10 1234 5678"})]})]})]})]})]})};export{b as component};
