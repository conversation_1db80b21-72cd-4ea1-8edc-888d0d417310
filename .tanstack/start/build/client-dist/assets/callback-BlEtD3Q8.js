import{u as w,r as f,j as s}from"./main-B6mr56sS.js";import{a as U,u as E}from"./use-auth-BT4cMsMh.js";import{a as x,k as A,n as D}from"./user-B82y3N16.js";const N={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,TSS_APP_BASE:"/",TSS_OUTPUT_PUBLIC_DIR:"/Users/<USER>/Project/sinochem-agent/.output/public",TSS_SERVER_FN_BASE:"/_serverFn",TSS_SPA_MODE:"false",VITE_CALCULATE_APP_ID:"680919cbac364a80b24306137e5debeb",VITE_DASHSCOPE_API_KEY:"sk-5ff58b88af7343b7bbb388079e1442f2",VITE_DINGTALK_CLIENT_ID:"dingaikvuvacp2rme0ef",VITE_DINGTALK_CLIENT_SECRET:"oPUfSwvFTF5zB1BaUatpHEnZesmyynsTAciBiYg9sagT0I4TxdUKJulGnmVhFQnP",VITE_TURSO_AUTH_TOKEN:"***************************************************************************************************************************************************************************************************************************************************************************",VITE_TURSO_DATABASE_URL:"libsql://sinochem-agent-vercel-icfg-************************.aws-ap-northeast-1.turso.io",VITE_YOUR_APP_ID:"622fbd2ef57c413baafa29527d205414",VITE_YOUR_FILE_APP_ID:"38669993697942e6a8ac1a9f1aa591e0"};async function j(T,c,r){try{const o=await fetch("/api/auth/dingtalk-callback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({clientId:c,clientSecret:r,code:T,grantType:"authorization_code"})}),I=await o.json();if(!o.ok)throw console.error("API代理调用失败:",I),new Error(I.message||`API调用失败: ${o.status} ${o.statusText}`);return I}catch(o){return console.error("调用钉钉API获取用户token失败:",o),null}}async function v(T,c="me"){try{const r=await fetch("/api/auth/dingtalk-userinfo",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({accessToken:T,unionId:c})}),o=await r.json();if(!r.ok)throw console.error("获取钉钉用户信息失败:",o),new Error(o.error||`获取用户信息失败: ${r.status} ${r.statusText}`);return o}catch(r){return console.error("调用钉钉用户信息API失败:",r),null}}const O=function(){const c=w(),{login:r}=U(),[o,I]=f.useState("loading"),[_,b]=f.useState("正在处理钉钉登录...");return f.useEffect(()=>{(async()=>{try{const d=new URLSearchParams(window.location.search),l=d.get("code")||d.get("authCode"),h=d.get("state");if(!l)throw new Error("未获取到授权码");if(h!=="dingtalk_login")throw new Error("状态参数验证失败");const k="dingaikvuvacp2rme0ef",i="oPUfSwvFTF5zB1BaUatpHEnZesmyynsTAciBiYg9sagT0I4TxdUKJulGnmVhFQnP";console.log("=== 钉钉配置调试信息 ==="),console.log("环境变量 VITE_DINGTALK_CLIENT_ID:","dingaikvuvacp2rme0ef"),console.log("环境变量 VITE_DINGTALK_CLIENT_SECRET:","oPUfSwvFTF5zB1BaUatpHEnZesmyynsTAciBiYg9sagT0I4TxdUKJulGnmVhFQnP"),console.log("实际使用的 clientId:",k),console.log("实际使用的 clientSecret:",i),console.log("clientId 长度:",k?.length),console.log("clientSecret 长度:",i?.length),console.log("所有环境变量:",N),console.log("收到钉钉授权码 (authCode):",l);const p={code:l,state:h,timestamp:new Date().toISOString(),clientId:k,clientSecret:i?`${i.substring(0,4)}****${i.substring(i.length-4)}`:"undefined",redirectUri:window.location.origin+"/auth/dingtalk/callback"};console.log("钉钉登录数据:",p);const n=await j(l,k,i);console.log("钉钉用户token响应:",n);let a,g,e=null;n&&(n.accessToken||n.fallback)?n.fallback?(console.warn("使用fallback token，钉钉API连接失败"),g=n.fallback.accessToken,a=`dingtalk_fallback_${l.substring(0,8)}`,console.log("使用fallback token:",{accessToken:g,reason:n.message,suggestions:n.suggestions}),e={nick:"钉钉用户",unionId:a,openId:`fallback_${l.substring(0,8)}`,mobile:"未设置",avatarUrl:null}):(g=n.accessToken,console.log("使用钉钉API返回的token:",{accessToken:g,refreshToken:n.refreshToken,expireIn:n.expireIn,corpId:n.corpId}),e=await v(g),console.log("钉钉用户个人信息:",e),e&&e.unionId?a=e.unionId:n.corpId?a=`dingtalk_${n.corpId}`:a=`dingtalk_${l.substring(0,8)}`):(console.warn("钉钉API调用失败，使用fallback token"),a=`dingtalk_${l.substring(0,8)}`,g=`dingtalk_fallback_${l}`);let t;try{if(console.log("正在通过dingTalkUnionId查询用户:",a),t=await x(a),t){if(console.log("用户已存在:",t),e){const u={name:e.nick||t.name,mobile:e.mobile||t.mobile,avatar:e.avatarUrl||t.avatar,dingTalkUserId:e.openId||t.dingTalkUserId,updatedAt:new Date().toISOString()};console.log("正在更新用户信息:",u);try{await A(t.dingTalkUnionId,u),console.log("用户信息更新成功"),t={...t,...u}}catch(S){console.error("更新用户信息失败:",S)}}}else{console.log("用户不存在，正在创建新用户...");const u=e?{dingTalkUserId:e.openId,dingTalkUnionId:e.unionId||a,name:e.nick,avatar:e.avatarUrl,mobile:e.mobile}:{dingTalkUnionId:a,name:"钉钉用户",mobile:"未设置"};t=await D(u),console.log("新用户创建成功:",t)}}catch(u){console.error("数据库操作失败:",u),t={id:Date.now(),dingTalkUnionId:e?.unionId||a,isAdmin:!1,token:2e4,requestTimes:0,name:e?.nick||"钉钉用户",mobile:e?.mobile||"未设置",avatar:e?.avatarUrl,dingTalkUserId:e?.openId,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}}e&&localStorage.setItem("dingtalk_user_info",JSON.stringify(e)),E.getState().setUser(t),r(t,g),I("success"),b("登录成功，正在跳转...");let m=localStorage.getItem("redirectAfterLogin");m||(m=t.isAdmin?"/dashboard":"/ai"),localStorage.removeItem("redirectAfterLogin"),setTimeout(()=>{c({to:m})},1500)}catch(d){console.error("钉钉登录失败:",d),I("error"),b(d instanceof Error?d.message:"登录失败，请重试"),setTimeout(()=>{c({to:"/auth/login"})},3e3)}})()},[]),s.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-white",children:s.jsxs("div",{className:"w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl p-8 flex flex-col items-center border border-blue-100",children:[s.jsxs("div",{className:"flex items-center justify-center w-16 h-16 rounded-2xl bg-white mb-6 shadow-lg",children:[o==="loading"&&s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),o==="success"&&s.jsx("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),o==="error"&&s.jsx("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]}),s.jsxs("h2",{className:"text-xl font-semibold mb-2 text-center text-gray-800",children:[o==="loading"&&"处理中",o==="success"&&"登录成功",o==="error"&&"登录失败"]}),s.jsx("p",{className:"text-gray-600 text-sm text-center",children:_}),o==="error"&&s.jsx("button",{onClick:()=>c({to:"/auth/login"}),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"返回登录"})]})})};export{O as component};
