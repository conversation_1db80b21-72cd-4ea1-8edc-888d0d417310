import{j as s,L as r}from"./main-B6mr56sS.js";import{C as c,a as l,b as i,c as n,d as t}from"./card-CdUhOvNY.js";import{B as e}from"./badge-czNfS6QW.js";import{B as d}from"./button-B5f_3GyS.js";import{A as x}from"./arrow-left-BrTwqmNq.js";import{c as a}from"./createLucideIcon-3aflogHk.js";import"./index-_TRYHs0w.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],m=a("sparkles",o);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],j=a("target",h);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],N=a("zap",p),C=function(){return s.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:[s.jsx("div",{className:"mb-4",children:s.jsx(r,{to:"/",children:s.jsxs(d,{variant:"ghost",className:"mb-4",children:[s.jsx(x,{className:"h-4 w-4 mr-2"}),"返回"]})})}),s.jsxs("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-4xl font-bold mb-2",children:"模型中心"}),s.jsx("p",{className:"text-muted-foreground",children:"探索中化集团先进的AI模型解决方案"})]}),s.jsxs(c,{className:"mb-8 border-2",children:[s.jsxs(l,{children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx(m,{className:"h-5 w-5 text-primary"}),s.jsx(e,{variant:"default",className:"text-sm",children:"推荐模型"})]}),s.jsx(i,{className:"text-2xl",children:"Sinochem Xinghai 兴海大模型"}),s.jsx(n,{children:"中化兴海自主研发的企业级大语言模型，专为化工行业智能化转型而设计"})]}),s.jsxs(t,{children:[s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsxs("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[s.jsx(j,{className:"h-4 w-4"}),"核心能力"]}),s.jsxs("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[s.jsx("li",{children:"• 化工领域专业知识理解与生成"}),s.jsx("li",{children:"• 安全规程智能分析与预警"}),s.jsx("li",{children:"• 生产流程优化建议"}),s.jsx("li",{children:"• 设备故障诊断与预测"}),s.jsx("li",{children:"• 供应链智能决策支持"})]})]}),s.jsxs("div",{children:[s.jsxs("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[s.jsx(N,{className:"h-4 w-4"}),"技术特色"]}),s.jsxs("ul",{className:"space-y-2 text-sm text-muted-foreground",children:[s.jsx("li",{children:"• 千亿级参数规模，行业知识深度训练"}),s.jsx("li",{children:"• 多模态融合，支持文本、图像、数据"}),s.jsx("li",{children:"• 实时学习与知识更新机制"}),s.jsx("li",{children:"• 企业级安全与合规保障"}),s.jsx("li",{children:"• 私有化部署与API服务双模式"})]})]})]}),s.jsx("div",{className:"mt-6 pt-6 border-t",children:s.jsxs("div",{className:"flex flex-wrap gap-2",children:[s.jsx(e,{variant:"secondary",children:"化工安全"}),s.jsx(e,{variant:"secondary",children:"生产优化"}),s.jsx(e,{variant:"secondary",children:"质量管控"}),s.jsx(e,{variant:"secondary",children:"供应链"}),s.jsx(e,{variant:"secondary",children:"研发创新"})]})})]})]})]})};export{C as component};
