import{j as s,L as e}from"./main-B6mr56sS.js";import{C as l,a as i,b as c,c as n,d as x}from"./card-CdUhOvNY.js";import{B as r}from"./button-B5f_3GyS.js";import{A as a}from"./arrow-left-BrTwqmNq.js";import"./index-_TRYHs0w.js";import"./createLucideIcon-3aflogHk.js";const p=function(){return s.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[s.jsx(r,{variant:"ghost",className:"mb-4",asChild:!0,children:s.jsxs(e,{to:"/",children:[s.jsx(a,{className:"mr-2 h-4 w-4"}),"返回"]})}),s.jsxs(l,{children:[s.jsxs(i,{children:[s.jsx(c,{className:"text-3xl font-bold",children:"服务条款"}),s.jsx(n,{className:"text-lg",children:"最后更新日期：2024年12月"})]}),s.jsxs(x,{className:"prose prose-gray max-w-none",children:[s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"1. 接受条款"}),s.jsx("p",{children:'通过访问和使用中化智能助手服务（以下简称"服务"），您同意受这些服务条款（"条款"）的约束。如果您不同意这些条款的任何部分，请不要使用我们的服务。'})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"2. 服务描述"}),s.jsx("p",{children:"中化智能助手提供基于人工智能的对话服务，旨在帮助用户获取信息、解答问题和提供建议。我们保留随时修改、暂停或终止服务的权利，恕不另行通知。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"3. 用户账户"}),s.jsx("p",{children:"您可能需要创建账户才能使用某些功能。您同意："}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsx("li",{children:"提供准确、最新和完整的注册信息"}),s.jsx("li",{children:"维护并及时更新您的账户信息"}),s.jsx("li",{children:"保护您的账户凭据的安全性和机密性"}),s.jsx("li",{children:"对您的账户下发生的所有活动负责"}),s.jsx("li",{children:"立即通知我们任何未经授权的使用或安全漏洞"})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"4. 用户行为"}),s.jsx("p",{children:"您同意不将服务用于以下目的："}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsx("li",{children:"违反任何适用的法律或法规"}),s.jsx("li",{children:"侵犯他人的权利，包括知识产权"}),s.jsx("li",{children:"传播恶意软件或其他有害代码"}),s.jsx("li",{children:"收集或存储其他用户的个人信息"}),s.jsx("li",{children:"干扰或破坏服务的完整性或性能"}),s.jsx("li",{children:"使用自动化手段访问服务，包括机器人、爬虫或类似工具"}),s.jsx("li",{children:"试图未经授权访问服务的任何部分"})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"5. 知识产权"}),s.jsx("p",{children:"服务及其原创内容、特性和功能归中化集团所有，并受国际版权、商标、专利、商业秘密和其他知识产权法律的保护。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"6. 用户内容"}),s.jsx("p",{children:"您保留对您提交给服务的内容的所有权。但是，通过提交内容，您授予我们在全球范围内、免版税、非独占的许可，以使用、复制、修改、改编、发布、翻译和分发此类内容，仅限于运营和改进服务的目的。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"7. 免责声明"}),s.jsx("p",{children:'服务按"现状"和"可用"基础提供，不提供任何明示或暗示的保证。我们不保证：'}),s.jsxs("ul",{className:"list-disc pl-6 space-y-2",children:[s.jsx("li",{children:"服务将不间断、安全或无错误"}),s.jsx("li",{children:"通过服务获得的信息准确或可靠"}),s.jsx("li",{children:"服务的质量将满足您的期望"}),s.jsx("li",{children:"服务中的任何错误都将得到纠正"})]})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"8. 责任限制"}),s.jsx("p",{children:"在任何情况下，中化集团或其关联公司、董事、员工、代理或供应商均不对任何间接、附带、特殊、后果性或惩罚性损害负责，包括但不限于利润损失、数据丢失、使用损失或业务中断，即使已被告知此类损害的可能性。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"9. 赔偿"}),s.jsx("p",{children:"您同意赔偿并使中化集团及其关联公司、董事、员工、代理和供应商免受因您使用服务、违反这些条款或侵犯任何第三方权利而引起的任何索赔、损害、义务、损失、责任、成本或债务。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"10. 终止"}),s.jsx("p",{children:"我们保留在任何时候以任何理由立即终止或暂停您的账户和对服务的访问的权利，无需事先通知或承担责任，包括但不限于您违反这些条款的情况。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"11. 条款变更"}),s.jsx("p",{children:"我们保留随时修改或替换这些条款的权利。如果变更重大，我们将尽量在任何新条款生效前至少30天提供通知。在修订生效后继续访问或使用我们的服务，即表示您同意受修订条款的约束。"})]}),s.jsxs("section",{className:"mb-6",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"12. 适用法律"}),s.jsx("p",{children:"这些条款应受中华人民共和国法律管辖，不考虑其法律冲突规定。您同意接受位于中国北京的法院的专属管辖权。"})]}),s.jsxs("section",{children:[s.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"13. 联系我们"}),s.jsx("p",{children:"如果您对这些条款有任何疑问，请通过以下方式联系我们："}),s.jsxs("p",{className:"mt-2",children:["电子邮件：",s.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})]}),s.jsxs("p",{children:["电话：",s.jsx("a",{href:"tel:+861012345678",className:"text-blue-600 hover:underline",children:"+86 10 1234 5678"})]})]})]})]})]})};export{p as component};
